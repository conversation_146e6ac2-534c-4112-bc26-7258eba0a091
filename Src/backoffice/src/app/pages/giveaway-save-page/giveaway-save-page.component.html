<app-section-title [reloadFn]="!isNew ? form.dataRequest.run : null">
  <span *ngIf="!isNew">
    <span *ngIf="canEdit()">Editar sorteo</span>
    <span *ngIf="!canEdit()">Detalle del sorteo</span>
  </span>
  <span *ngIf="isNew">Crear sorteo</span>

  <ng-container *ngIf="!isNew">
    <ng-template #extras>
      <strong class="m-0 ms-2 app-fs-14 text-muted">
        Id #{{ form.dataRequest.body()?.data?.id }}
      </strong>
    </ng-template>
  </ng-container>
</app-section-title>

<div class="container-fluid">
  <app-request [requests]="form.getRequests()">
    <div class="box">
      <form class="form-default" nz-form nzLayout="vertical" [formGroup]="form.group" (ngSubmit)="form.submit()">
        <app-form-section-title [title]="'Configuración de participantes'" icon="usergroup-add" [isFirst]="true" />

        <div class="ps-4">
          <div class="mb-3">
            <p><strong>Origen de los participantes</strong></p>

            <nz-radio-group formControlName="use_quiz">
              <label class="d-block mb-1" nz-radio [nzValue]="true">Partida</label>
              <label class="d-block mb-1" nz-radio [nzValue]="false">Carga manual</label>
            </nz-radio-group>
          </div>

          <ng-container *ngIf="form.group.get('use_quiz')?.value; else manualParticipantsInputTpl">
            <div class="row">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label nzRequired>Canal</nz-form-label>
                  <nz-select formControlName="channel" [compareWith]="form.compareFn" nzAllowClear [nzShowArrow]="true" nzPlaceHolder="Elegir canal">
                    <ng-container *ngFor="let channel of form.getCombo('channels_for_giveaways')">
                      <nz-option [nzValue]="channel" [nzLabel]="channel.name + ' (' + channel.slug + ')'"></nz-option>
                    </ng-container>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>

              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label nzRequired>Partida</nz-form-label>
                  <nz-select formControlName="quiz" [compareWith]="form.compareFn" [nzShowArrow]="true">
                    <ng-container *ngFor="let quiz of form.getCombo('quizzes_for_giveaways')">
                      <nz-option *ngIf="quiz.channel_id === form.group.get('channel')?.value?.id" [nzValue]="quiz" [nzLabel]="quiz.name"></nz-option>
                    </ng-container>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>

            <div class="row">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label nzRequired>Modo de participantes</nz-form-label>
                  <nz-select formControlName="allowed_participants_mode" [compareWith]="form.compareFn" nzPlaceHolder="Elegir">
                    <ng-container *ngFor="let mode of form.getCombo('giveaway_allowed_participants_modes')">
                      <ng-container *ngIf="mode.name !== 'CorrectAnswers'">
                        <nz-option [nzValue]="mode.name" [nzLabel]="mode.value.label"></nz-option>
                      </ng-container>

                      <ng-container *ngIf="mode.name === 'CorrectAnswers' && quiz()?.data?.config?.data?.scoringMode !== 'Traditional'">
                        <nz-option [nzValue]="mode.name" [nzLabel]="mode.value.label"></nz-option>
                      </ng-container>
                    </ng-container>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>

              <ng-container *ngIf="form.group.get('allowed_participants_mode')?.value === 'CorrectAnswers'">
                <nz-form-item class="col-60 col-lg-30 col-xl-15">
                  <nz-form-control>
                    <nz-form-label nzRequired>Respuestas correctas para participar</nz-form-label>
                    <nz-input-number class="w-100" formControlName="min_correct_answers" [nzMin]="1" [nzMax]="100"></nz-input-number>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>

              <ng-container *ngIf="form.group.get('allowed_participants_mode')?.value === 'TopRanking'">
                <nz-form-item class="col-60 col-lg-30 col-xl-15">
                  <nz-form-control>
                    <nz-form-label nzRequired>Cantidad de jugadores en el top</nz-form-label>
                    <nz-input-number class="w-100" formControlName="max_top_ranking" [nzMin]="1" [nzMax]="100"></nz-input-number>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>
            </div>
          </ng-container>

          <ng-template #manualParticipantsInputTpl>
            <div class="row">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label>Archivo CSV</nz-form-label>
                  <app-form-upload [control]="form.getControl('participants_excel')" concept="giveaway:participants_excel" type="button" />
                  <input nz-input formControlName="participants_excel">
                </nz-form-control>
              </nz-form-item>
            </div>
          </ng-template>

          <button type="button" class="btn btn-primary ms-auto" [disabled]="!canResolveParticipants()" (click)="resolveParticipantsRequest.run()">Generar previsualización de participantes</button>

          <div class="mt-4">
            <p><strong>Listado de participantes</strong></p>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Buscar</nz-form-label>
                <input nz-input placeholder="Buscar en campos personalizados..." (input)="searchText.set($any($event.target).value)">
              </nz-form-control>
            </nz-form-item>

            <app-request [request]="resolveParticipantsRequest">
              <nz-table #sortTable
                nzPaginationType="small"
                [nzScroll]="{ x: '1500px' }"
                [nzData]="filteredParticipants()"
                [nzPageSize]="10"
                [nzShowTotal]="paginationRangeTpl"
              >
                <thead>
                  <tr>
                    <ng-container *ngFor="let column of participantsTableColumns()">
                      <th
                        nzWidth="200px"
                        [nzSortFn]="column.sortFn"
                        [nzSortOrder]="column.sortOrder"
                        [nzSortDirections]="column.sortDirections"
                        (nzSortOrderChange)="onSortChange(column.title, $event)"
                      >
                        {{ column.title }}
                      </th>
                    </ng-container>

                    <th nzWidth="60px" nzAlign="center" nzRight="0">Acciones</th>

                    <th nzWidth="0px"></th>
                  </tr>
                </thead>

                <tbody>
                  <ng-container *ngFor="let item of sortTable.data">
                    <tr [class.opacity-50]="blackList().indexOf(item.id) !== -1" [class.text-decoration-line-through]="blackList().indexOf(item.id) !== -1">
                      <ng-container *ngFor="let field of item.custom_fields">
                        <td>{{ field.value }}</td>
                      </ng-container>

                      <td nzAlign="center" nzRight="0">
                        <ng-container *ngIf="blackList()?.indexOf(item.id) === -1">
                          <button type="button" class="btn-styless text-danger" (click)="addParticipantToBlacklist(item)">
                            <i nz-icon nzType="delete"></i>
                          </button>
                        </ng-container>

                        <ng-container *ngIf="blackList()?.indexOf(item.id) !== -1">
                          <button type="button" class="btn-styless text-success" (click)="removeParticipantOfBlacklist(item)">
                            <i nz-icon nzType="undo"></i>
                          </button>
                        </ng-container>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </nz-table>

              <ng-template #paginationRangeTpl let-range="range" let-total>
                {{ range[0] | number }}-{{ range[1] | number  }} de {{ total | number  }} registros
              </ng-template>
            </app-request>
          </div>
        </div>

        <app-form-section-title [title]="'Información del sorteo'" icon="info-circle" />

        <div class="ps-4">
          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label nzRequired>Nombre del sorteo</nz-form-label>
                <input nz-input formControlName="name">
              </nz-form-control>
            </nz-form-item>

            <ng-container *ngIf="!isNew">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label>
                    <span nz-typography nzCopyable [nzCopyText]="form.dataRequest.body()?.data?.monitorPin">Pin para ingresar al sorteo</span>
                  </nz-form-label>
                  <input nz-input [ngModel]="form.dataRequest.body()?.data?.monitorPin" [ngModelOptions]="{ standalone: true }" disabled>
                </nz-form-control>
              </nz-form-item>
            </ng-container>
          </div>

          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Mensaje principal</nz-form-label>
                <textarea nz-input formControlName="message_primary" [nzAutosize]="{ minRows: 2, maxRows: 5 }"></textarea>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Mensaje secundario</nz-form-label>
                <textarea nz-input formControlName="message_secondary" [nzAutosize]="{ minRows: 2, maxRows: 5 }"></textarea>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <app-form-section-title [title]="'Configuración de premios'" icon="trophy" />

        <div class="ps-4">
          <div formArrayName="prizes">
            <ng-container *ngFor="let control of form.asFA(form.group.get('prizes'))?.controls; let i = index">
              <div [formGroupName]="i">
                <div class="row align-items-end">
                  <nz-divider *ngIf="i > 0" class="m-0 mb-3"></nz-divider>

                  <div class="d-flex align-items-center my-3">
                    <p class="fw-bolder my-0 me-2">Premio {{ i + 1 }}

                      <ng-container *ngIf="i !== 0 && canEdit()">
                        <button type="button" class="btn-styless text-danger ms-2" (click)="form.arrayRemove('prizes', i)">
                          <i nz-icon nzType="delete"></i>
                        </button>
                      </ng-container>
                    </p>
                  </div>

                  <div class="row">
                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label nzRequired>Nombre</nz-form-label>
                        <input nz-input formControlName="name">
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Descripción</nz-form-label>
                        <input nz-input formControlName="description">
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label nzRequired>Cantidad</nz-form-label>
                        <nz-input-number class="w-100" formControlName="quantity" [nzMin]="1"></nz-input-number>
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Imagen del premio</nz-form-label>
                        <app-form-upload
                          [control]="form.getControl('prizes.' + i + '.image')"
                          concept="prize_image"
                          type="button"
                        ></app-form-upload>
                        <input nz-input formControlName="image">
                      </nz-form-control>
                    </nz-form-item>
                  </div>

                  <div class="row align-items-end">
                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label nzRequired>Duración de la tirada (en segundos)</nz-form-label>
                        <nz-input-number class="w-100" formControlName="draw_duration_seconds" [nzMin]="1"></nz-input-number>
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <label class="m-0 my-1" nz-checkbox formControlName="draw_all_at_once">Revelar todos los ganadores a la vez</label>
                      </nz-form-control>
                    </nz-form-item>
                  </div>
                </div>
              </div>
            </ng-container>

            <ng-container *ngIf="canEdit()">
              <div class="col-60 col-lg-30 col-xl-20 col-xxl-15">
                <div class="d-flex h-100 justify-content-center my-4">
                  <button nz-button type="button" nzType="dashed" nzShape="round" class="" (click)="form.arrayAdd(form.asFA(form.group.get('prizes')), 'prizes')">
                    <i nz-icon nzType="plus"></i> Agregar premio
                  </button>
                </div>
              </div>
            </ng-container>
          </div>
        </div>

        <app-form-section-title [title]="'Multimedia personalizada del sorteo'" icon="file-image" />

        <div class="ps-4">
          <div formGroupName="multimedia_config">
            <div class="row align-items-end">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label>Logo</nz-form-label>
                  <app-form-upload
                    [control]="form.getControl('multimedia_config.logo')"
                    concept="multimedia_config.logo"
                    type="button"
                  ></app-form-upload>
                  <input nz-input formControlName="logo">
                </nz-form-control>
              </nz-form-item>

              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label>Video de fondo vertical (1080x1920)</nz-form-label>
                  <app-form-upload
                    [control]="form.getControl('multimedia_config.bg_video')"
                    concept="multimedia_config_bg_video"
                    type="button"
                  ></app-form-upload>
                  <input nz-input formControlName="bg_video">
                </nz-form-control>
              </nz-form-item>

              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label>Video de fondo monitor horizontal (1920x1080)</nz-form-label>
                  <app-form-upload
                    [control]="form.getControl('multimedia_config.monitor_bg_video')"
                    concept="multimedia_config_monitor_bg_video"
                    type="button"
                  ></app-form-upload>
                  <input nz-input formControlName="monitor_bg_video">
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="row align-items-end">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <label class="m-0 my-1" nz-checkbox formControlName="audio_enabled">Activar sonido</label>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <app-form-section-title [title]="'Estilos visuales personalizados del sorteo'" icon="format-painter" />

        <div class="ps-4" formGroupName="style_config">
          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Color principal</nz-form-label>
                <div class="d-flex align-items-center">
                  <ngx-colors ngx-colors-trigger formControlName="primary" acceptLabel='Aceptar' cancelLabel='Cancelar'/>
                  <span class="text-uppercase ms-2">{{ form.getControl('style_config.primary').value }}</span>
                </div>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Color secundario</nz-form-label>
                <div class="d-flex align-items-center">
                  <ngx-colors ngx-colors-trigger formControlName="secondary" acceptLabel='Aceptar' cancelLabel='Cancelar'/>
                  <span class="text-uppercase ms-2">{{ form.getControl('style_config.secondary').value }}</span>
                </div>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Color de texto de botón principal</nz-form-label>
                <div class="d-flex align-items-center">
                  <ngx-colors ngx-colors-trigger formControlName="btn_text_primary" acceptLabel='Aceptar' cancelLabel='Cancelar'/>
                  <span class="text-uppercase ms-2">{{ form.getControl('style_config.btn_text_primary').value }}</span>
                </div>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Color de fondo de botón principal</nz-form-label>
                <div class="d-flex align-items-center">
                  <ngx-colors ngx-colors-trigger formControlName="btn_bg_primary" acceptLabel='Aceptar' cancelLabel='Cancelar'/>
                  <span class="text-uppercase ms-2">{{ form.getControl('style_config.btn_bg_primary').value }}</span>
                </div>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <ng-container *ngIf="canEdit()">
          <app-form-footer [form]="form" [buttonText]="isNew ? 'Crear' : 'Guardar cambios'" />
        </ng-container>
      </form>
    </div>

    <ng-container *ngIf="form.dataRequest.body()?.data?.status === 'Completed'">
      <div class="box mt-3">
        <app-form-section-title [title]="'Ganadores del sorteo'" icon="trophy" [isFirst]="true" />

        <ng-container *ngIf="form.dataRequest.body()?.data?.winners?.length">
          <nz-table #table
            nzPaginationType="small"
            [nzScroll]="{ x: '1000px' }"
            [nzData]="form.dataRequest.body()?.data?.winners!"
            [nzPageSize]="20"
            [nzShowTotal]="paginationRangeTpl"
          >
            <thead>
              <tr>
                <th nzWidth="140px">Premio</th>

                <th *ngFor="let field of form.dataRequest.body()!.data.winners![0].customFields"
                  nzWidth="140px"
                >
                  {{ field.label }}
                </th>

                <th nzWidth="140px">Posición en la partida</th>
                <th nzWidth="140px">Puntos en la partida</th>
              </tr>
            </thead>

            <tbody>
              <ng-container *ngFor="let item of table.data">
                <tr>
                  <td>{{ item.prizeName }}</td>

                  <td *ngFor="let field of item.customFields">
                    {{ field.value }}
                  </td>

                  <td>{{ item.position }}</td>

                  <td>{{ item.points }}</td>
                </tr>
              </ng-container>
            </tbody>
          </nz-table>
        </ng-container>

        <ng-template #paginationRangeTpl let-range="range" let-total>
          {{ range[0] | number }}-{{ range[1] | number  }} de {{ total | number  }} registros
        </ng-template>
      </div>
    </ng-container>
  </app-request>
</div>
