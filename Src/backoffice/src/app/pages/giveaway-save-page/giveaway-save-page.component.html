<app-section-title [reloadFn]="!isNew ? form.dataRequest.run : null">
  <span *ngIf="!isNew">Editar sorteo</span>
  <span *ngIf="isNew">Crear sorteo</span>

  <ng-container *ngIf="!isNew">
    <ng-template #extras>
      <strong class="m-0 ms-2 app-fs-14 text-muted">
        Id #{{ form.dataRequest.body()?.data?.id }}
      </strong>
    </ng-template>
  </ng-container>
</app-section-title>

<div class="container-fluid">
  <div class="box">
    <app-request [requests]="form.getRequests()">
      <form class="form-default" nz-form nzLayout="vertical" [formGroup]="form.group" (ngSubmit)="form.submit()">
        <app-form-section-title [title]="'Información del sorteo'" icon="info-circle" [isFirst]="true" />

        <div class="ps-4">
          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label nzRequired>Nombre del sorteo</nz-form-label>
                <input nz-input formControlName="name">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label nzRequired>Canal</nz-form-label>
                <nz-select formControlName="channel" [compareWith]="form.compareFn" nzAllowClear [nzShowArrow]="true" nzPlaceHolder="Elegir canal">
                  <ng-container *ngFor="let channel of form.getCombo('channels_for_giveaways')">
                    <nz-option [nzValue]="channel" [nzLabel]="channel.name + ' (' + channel.slug + ')'"></nz-option>
                  </ng-container>
                </nz-select>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label nzRequired>Partida</nz-form-label>
                <nz-select formControlName="quiz" [compareWith]="form.compareFn" [nzShowArrow]="true">
                  <ng-container *ngFor="let quiz of form.getCombo('quizzes_for_giveaways')">
                    <nz-option *ngIf="quiz.channel_id === form.group.get('channel')?.value?.id" [nzValue]="quiz" [nzLabel]="quiz.name"></nz-option>
                  </ng-container>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label nzRequired>Modo de participantes</nz-form-label>
                <nz-select formControlName="allowed_participants_mode" [compareWith]="form.compareFn" nzPlaceHolder="Elegir">
                  <ng-container *ngFor="let giveawayMode of form.getCombo('giveaway_allowed_participants_modes')">
                    <nz-option [nzValue]="giveawayMode.name" [nzLabel]="giveawayMode.value.label"></nz-option>
                  </ng-container>
                </nz-select>
              </nz-form-control>
            </nz-form-item>

            <ng-container *ngIf="form.group.get('mode')?.value === 'CorrectAnswers'">
              <nz-form-item class="col-60 col-lg-30 col-xl-15">
                <nz-form-control>
                  <nz-form-label nzRequired>Respuestas correctas para participar</nz-form-label>
                  <nz-input-number class="w-100" formControlName="min_correct_answers" [nzMin]="1" [nzMax]="120"></nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </ng-container>
          </div>
        </div>

        <app-form-section-title [title]="'Configuración de premios'" icon="trophy" />

        <div class="ps-4">
          <div formArrayName="prizes">
            <ng-container *ngFor="let control of form.asFA(form.group.get('prizes'))?.controls; let i = index">
              <div [formGroupName]="i">
                <div class="row align-items-end">
                  <nz-divider *ngIf="i > 0" class="m-0 mb-3"></nz-divider>

                  <div class="d-flex align-items-center my-3">
                    <p class="fw-bolder my-0 me-2">Premio {{ i + 1 }}

                      <ng-container *ngIf="i !== 0">
                        <button type="button" class="btn-styless text-danger ms-2" (click)="form.arrayRemove('prizes', i)">
                          <i nz-icon nzType="delete"></i>
                        </button>
                      </ng-container>
                    </p>
                  </div>

                  <div class="row">
                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label nzRequired>Nombre</nz-form-label>
                        <input nz-input formControlName="name">
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Descripción</nz-form-label>
                        <input nz-input formControlName="description">
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label nzRequired>Cantidad</nz-form-label>
                        <nz-input-number class="w-100" formControlName="quantity" [nzMin]="1"></nz-input-number>
                      </nz-form-control>
                    </nz-form-item>
                  </div>

                  <div class="row">
                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Posición mínima</nz-form-label>
                        <nz-input-number class="w-100" formControlName="min_position" [nzMin]="0"></nz-input-number>
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Posición máxima</nz-form-label>
                        <nz-input-number class="w-100" formControlName="max_position" [nzMin]="0"></nz-input-number>
                      </nz-form-control>
                    </nz-form-item>

                    <nz-form-item class="col-60 col-lg-30 col-xl-15">
                      <nz-form-control>
                        <nz-form-label>Imagen del premio</nz-form-label>
                        <app-form-upload
                          [control]="form.getControl('prizes.' + i + '.image')"
                          concept="prize_image"
                          type="button"
                        ></app-form-upload>
                        <input nz-input formControlName="image">
                      </nz-form-control>
                    </nz-form-item>
                  </div>
                </div>
              </div>
            </ng-container>

            <div class="col-60 col-lg-30 col-xl-20 col-xxl-15">
              <div class="d-flex h-100 justify-content-center my-4">
                <button nz-button type="button" nzType="dashed" nzShape="round" class="" (click)="form.arrayAdd(form.asFA(form.group.get('prizes')), 'prizes')">
                  <i nz-icon nzType="plus"></i> Agregar premio
                </button>
              </div>
            </div>
          </div>
        </div>

        <app-form-section-title [title]="'Multimedia personalizada del sorteo'" icon="file-image" />

        <div class="ps-4">
          <div class="row align-items-end">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Logo</nz-form-label>
                <app-form-upload
                  [control]="form.getControl('logo')"
                  concept="giveaway_logo"
                  type="button"
                ></app-form-upload>
                <input nz-input formControlName="logo">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Video de fondo vertical (1080x1920)</nz-form-label>
                <app-form-upload
                  [control]="form.getControl('bg_video')"
                  concept="giveaway_bg_video"
                  type="button"
                ></app-form-upload>
                <input nz-input formControlName="bg_video">
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Video de fondo monitor horizontal (1920x1080)</nz-form-label>
                <app-form-upload
                  [control]="form.getControl('monitor_bg_video')"
                  concept="giveaway_monitor_bg_video"
                  type="button"
                ></app-form-upload>
                <input nz-input formControlName="monitor_bg_video">
              </nz-form-control>
            </nz-form-item>
          </div>

          <div class="row align-items-end">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <label class="m-0 my-1" nz-checkbox formControlName="audio_enabled">Activar sonido</label>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Duración de la animación</nz-form-label>
                <nz-input-number class="w-100" formControlName="draw_duration_seconds" [nzMin]="1"></nz-input-number>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <app-form-section-title [title]="'Estilos visuales personalizados del sorteo'" icon="format-painter" />

        <div class="ps-4">
          <div class="row">
            <nz-form-item class="col-60 col-lg-30 col-xl-15">
              <nz-form-control>
                <nz-form-label>Color principal</nz-form-label>
                <div class="d-flex align-items-center">
                  <ngx-colors ngx-colors-trigger formControlName="color_primary" acceptLabel='Aceptar' cancelLabel='Cancelar'/>
                  <span class="text-uppercase ms-2">{{ form.getControl('color_primary').value }}</span>
                </div>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <app-form-footer [form]="form" [buttonText]="isNew ? 'Crear' : 'Guardar cambios'" />
      </form>
    </app-request>
  </div>
</div>
