import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { SectionTitleComponent } from 'src/app/components/layouts/section-title/section-title/section-title.component';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { Form } from 'src/app/utils/form/form';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormModule } from 'src/app/utils/form/form.module';
import { FormSectionTitleComponent } from 'src/app/components/layouts/form-section-title/form-section-title.component';
import { FormFooterComponent } from 'src/app/components/layouts/form-footer/form-footer.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { AuthService } from 'src/app/services/auth.service';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { NgxColorsModule } from 'ngx-colors';
import { NzListModule } from 'ng-zorro-antd/list';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';
import { Giveaway } from 'src/app/commons/models/giveaway';
import { distinctUntilChanged, skip, startWith } from 'rxjs';

@Component({
  selector: 'app-giveaway-save-page',
  standalone: true,
  imports: [
    SharedModule,
    SectionTitleComponent,
    NzBreadCrumbModule,
    RouterModule,
    NzTagModule,
    RequestComponent,
    FormModule,
    FormSectionTitleComponent,
    FormFooterComponent,
    NzIconModule,
    NzTypographyModule,
    NzButtonModule,
    NzDividerModule,
    NzTypographyModule,
    NzToolTipModule,
    NgxColorsModule,
    NzListModule,
  ],
  templateUrl: './giveaway-save-page.component.html',
  styleUrls: ['./giveaway-save-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class GiveawaySavePageComponent {

  form: Form<Giveaway>;
  giveawayId: number = parseInt(this._route.snapshot.paramMap.get('giveawayId') || '');

  isNew!: boolean;

  constructor(
    public authS: AuthService,
    private _route: ActivatedRoute,
    private _giveawayHttpS: GiveawayHttpService,
    private _fb: FormBuilder,
    private _router: Router,
  ) {

    this.isNew = this.giveawayId ? false : true;

    this.form = new Form(this._fb.group({
      name: ['', [Validators.required]],
      channel: [null, [Validators.required]],
      quiz: [null, [Validators.required]],
      allowed_participants_mode: [null, [Validators.required]],
      min_correct_answers: [null],
      audio_enabled: [true],
      draw_duration_seconds: [10, [Validators.required]],
      prizes: this._fb.array([]),
      logo: [null],
      bg_video: [null],
      monitor_bg_video: [null],
      color_primary: [''],
    }), {
      onInit: (form) => {
        form.addInit(form.asFA(form.group.get('prizes')), form.state.get()?.prizes.length || 1, 'prizes');
      },
      onInitSubscriptions: (form) => {
        form.group.get('allowed_participants_mode')?.valueChanges.pipe(
          startWith(null),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          if (value !== 'CorrectAnswers') {
            form.group.get('min_correct_answers')?.setValue(null);
            form.group.get('min_correct_answers')?.removeValidators([Validators.required]);
          } else {
            form.group.get('min_correct_answers')?.setValue(1);
            form.group.get('min_correct_answers')?.setValidators([Validators.required]);
          }
        });
      },
      arrays: {
        prizes: {
          group: this._fb.group({
            id: [null],
            name: ['', [Validators.required]],
            description: [''],
            quantity: [1, [Validators.required]],
            min_position: [null],
            max_position: [null],
            image: [null],
          })
        },
      },
      combos: `channels_for_giveaways,quizzes_for_giveaways,giveaway_allowed_participants_modes`,
      dataRequest: !this.isNew ? {
        send: () => {
          const params = {
            with: 'quiz,prizes',
          };

          return this._giveawayHttpS.getOne(this.giveawayId, params);
        },
        body: 'giveaway',
        success: () => {
          let values = {
            ...this.form.dataRequest.body()?.raw,
            channel: this.form.dataRequest.body()?.raw.quiz.channel,
          }

          this.form.set(values);
        },
      } : undefined,
      request: {
        send: () => {
          const input = {
            ...this.form.group.getRawValue(),
          };

          return this.isNew
            ? this._giveawayHttpS.create(input)
            : this._giveawayHttpS.update(this.giveawayId, input);
        },
        success: (res) => {
          if (this.isNew) {
            this._router.navigate(['/sorteos']);
          } else {
            this.form.dataRequest.run();
          }
        },
        notify: true,
      },
    });
  }

  /* -------------------- */

  // onActionsSuccess(event: QuizActionsSuccessEvent) {
  //   switch (event.action) {
  //     case 'activate':
  //     case 'deactivate': {
  //       this.form.dataRequest.setBody(event.data);
  //       break;
  //     }

  //     case 'duplicate':
  //     case 'delete': {
  //       this.router.navigate(['/partidas']);
  //       break;
  //     }

  //     default: {
  //       this.form.dataRequest.run();
  //       break;
  //     }
  //   }
  // }

}
