import { ChangeDetectionStrategy, Component, computed, signal, WritableSignal } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { SectionTitleComponent } from 'src/app/components/layouts/section-title/section-title/section-title.component';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { Form } from 'src/app/utils/form/form';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormModule } from 'src/app/utils/form/form.module';
import { FormSectionTitleComponent } from 'src/app/components/layouts/form-section-title/form-section-title.component';
import { FormFooterComponent } from 'src/app/components/layouts/form-footer/form-footer.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { AuthService } from 'src/app/services/auth.service';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { NgxColorsModule } from 'ngx-colors';
import { NzListModule } from 'ng-zorro-antd/list';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';
import { Giveaway } from 'src/app/commons/models/giveaway';
import { distinctUntilChanged, skip, startWith } from 'rxjs';
import { NzTableModule } from 'ng-zorro-antd/table';
import { Request } from 'src/app/utils/request/request';
import { QuizHttpService } from 'src/app/services/http/quiz-http.service';
import { Quiz } from 'src/app/commons/models/quiz';

@Component({
  selector: 'app-giveaway-save-page',
  standalone: true,
  imports: [
    SharedModule,
    SectionTitleComponent,
    NzBreadCrumbModule,
    RouterModule,
    NzTagModule,
    RequestComponent,
    FormModule,
    FormSectionTitleComponent,
    FormFooterComponent,
    NzIconModule,
    NzTypographyModule,
    NzButtonModule,
    NzDividerModule,
    NzTypographyModule,
    NzToolTipModule,
    NgxColorsModule,
    NzListModule,
    NzTableModule
  ],
  templateUrl: './giveaway-save-page.component.html',
  styleUrls: ['./giveaway-save-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class GiveawaySavePageComponent {

  form: Form<Giveaway>;
  giveawayId: number = parseInt(this._route.snapshot.paramMap.get('giveawayId') || '');

  isNew!: boolean;
  canEdit = computed(() => {
    return this.isNew || (!this.isNew && this.form?.dataRequest.body()?.data.status === 'Pending');
  });

  quiz = signal<Quiz|null>(null);
  participants = signal<any[]>([]);
  blackList = signal<any[]>([]);
  searchText = signal('');

  // Estado de ordenamiento para las columnas
  sortStates = signal<{[key: string]: 'ascend' | 'descend' | null}>({});

  filteredParticipants = computed(() => {
    const text = this.searchText().toLowerCase();
    if (!text) return this.participants();

    return this.participants().filter(participant =>
      participant.name.toLowerCase().includes(text) ||
      participant.custom_fields.some((f: any) =>
        (f.value ?? '').toLowerCase().includes(text)
      )
    );
  });

  quizRequest = new Request({
    send: () => this._quizHttpS.getOne(this.form.getControl('quiz').value.id, { scopes: { withConfigFull: '' }}),
    success: (res) => {
      this.quiz.set(res.body.quiz);

      this.form.group.get('style_config')?.patchValue(this.quiz()?.data?.config?.data?.styleConfig);
      this.form.group.get('multimedia_config')?.patchValue(this.quiz()?.data?.config?.data?.multimediaConfig);
    },
  });

  resolveParticipantsRequest = new Request({
    send: () => this._giveawayHttpS.resolveParticipants({
      file: !this.quiz()?.data?.id ? this.form.getControl('participants_excel')?.value : null,
      quiz_id: this.quiz()?.data?.id ?? null,
      allowed_participants_mode: this.form.getControl('allowed_participants_mode')?.value,
      min_correct_answers: this.form.getControl('min_correct_answers')?.value,
      max_top_ranking: this.form.getControl('max_top_ranking')?.value,
    }),
    success: (res) => {
      this.participants?.set(res.body.participants);
    },
    notifySuccess: false,
  });

  importGiveawayParticipantsRequest = new Request({
    send: () => this._giveawayHttpS.importParticipants({ file: this.form.group.value.participants_excel }),
    notify: true,
  });

  constructor(
    public authS: AuthService,
    private _route: ActivatedRoute,
    private _giveawayHttpS: GiveawayHttpService,
    private _quizHttpS: QuizHttpService,
    private _fb: FormBuilder,
    private _router: Router,
  ) {

    this.isNew = this.giveawayId ? false : true;

    this.form = new Form(this._fb.group({
      name: ['', [Validators.required]],
      use_quiz: [true],
      channel: [null],
      quiz: [null],
      allowed_participants_mode: [null],
      min_correct_answers: [null],
      max_top_ranking: [null],
      participants_excel: [null],
      audio_enabled: [true],
      message_primary: [''],
      message_secondary: [''],
      prizes: this._fb.array([]),
      multimedia_config: this._fb.group({
        logo: [null],
        bg_video: [null],
        monitor_bg_video: [null],
      }),
      style_config: this._fb.group({
        primary: [''],
        secondary: [''],
        bg_primary: [''],
        bg_secondary: [''],
        btn_text_primary: [''],
        btn_bg_primary: [''],
      }),
    }), {
      onInit: (form) => {
        form.addInit(form.asFA(form.group.get('prizes')), form.state.get()?.prizes.length || 1, 'prizes');
      },
      onInitSubscriptions: (form) => {
        form.group.get('allowed_participants_mode')?.valueChanges.pipe(
          startWith(form.group.get('allowed_participants_mode')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          if (value === 'CorrectAnswers') {
            form.group.get('min_correct_answers')?.setValue(1);
            form.group.get('min_correct_answers')?.setValidators([Validators.required]);
          } else {
            form.group.get('min_correct_answers')?.setValue(null);
            form.group.get('min_correct_answers')?.removeValidators([Validators.required]);
          }

          if (value === 'TopRanking') {
            form.group.get('max_top_ranking')?.setValue(1);
            form.group.get('max_top_ranking')?.setValidators([Validators.required]);
          } else {
            form.group.get('max_top_ranking')?.setValue(null);
            form.group.get('max_top_ranking')?.removeValidators([Validators.required]);
          }

          this.participants.set([]);
        });

        form.group.get('quiz')?.valueChanges.pipe(
          startWith(form.group.get('quiz')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          if (value) {
            this.quizRequest.run();
            form.group.get('allowed_participants_mode')?.setValidators([Validators.required]);
          } else {
            console.log('no uso quiz')
            this.quiz.set(null);
            form.group.get('allowed_participants_mode')?.setValue(null);
            form.group.get('allowed_participants_mode')?.removeValidators([Validators.required]);
            form.group.get('min_correct_answers')?.setValue(null);
            form.group.get('min_correct_answers')?.removeValidators([Validators.required]);
            form.group.get('max_top_ranking')?.setValue(null);
            form.group.get('max_top_ranking')?.removeValidators([Validators.required]);

            form.group.updateValueAndValidity();
          }

          this.participants.set([]);
        });

        form.group.get('channel')?.valueChanges.pipe(
          startWith(form.group.get('channel')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          if (value) {
            form.group.get('quiz')?.setValue(null);
            form.group.get('style_config')?.reset();
            form.group.get('multimedia_config')?.reset();
          }
        });

        form.group.get('min_correct_answers')?.valueChanges.pipe(
          startWith(form.group.get('min_correct_answers')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          this.participants.set([]);
        });

        form.group.get('max_top_ranking')?.valueChanges.pipe(
          startWith(form.group.get('max_top_ranking')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          this.participants.set([]);
        });

        form.group.get('use_quiz')?.valueChanges.pipe(
          startWith(form.group.get('use_Quiz')?.value),
          distinctUntilChanged(),
          skip(1)
        ).subscribe(value => {
          if (value) {
            form.group.get('participants_excel')?.setValue(null);
            form.group.get('channel')?.addValidators([Validators.required]);
            form.group.get('channel')?.enable();
            form.group.get('quiz')?.addValidators([Validators.required]);
            form.group.get('quiz')?.enable();
            form.group.get('allowed_participants_mode')?.addValidators([Validators.required]);
            form.group.get('allowed_participants_mode')?.enable();
            form.group.get('min_correct_answers')?.enable();
            form.group.get('max_top_ranking')?.enable();
          } else {
            form.group.get('channel')?.setValue(null);
            form.group.get('channel')?.removeValidators([Validators.required]);
            form.group.get('channel')?.disable();
            form.group.get('quiz')?.setValue(null);
            form.group.get('quiz')?.removeValidators([Validators.required]);
            form.group.get('quiz')?.disable();
            form.group.get('allowed_participants_mode')?.setValue(null);
            form.group.get('allowed_participants_mode')?.removeValidators([Validators.required]);
            form.group.get('allowed_participants_mode')?.disable();
            form.group.get('min_correct_answers')?.setValue(null);
            form.group.get('min_correct_answers')?.removeValidators([Validators.required]);
            form.group.get('min_correct_answers')?.disable();
            form.group.get('max_top_ranking')?.setValue(null);
            form.group.get('max_top_ranking')?.removeValidators([Validators.required]);
            form.group.get('max_top_ranking')?.disable();
          }
        });
      },
      arrays: {
        prizes: {
          group: this._fb.group({
            id: [null],
            name: ['', [Validators.required]],
            description: [''],
            quantity: [1, [Validators.required]],
            min_position: [null],
            max_position: [null],
            image: [null],
            draw_duration_seconds: [10, [Validators.required]],
            draw_all_at_once: [false],
          })
        },
      },
      combos: `channels_for_giveaways,quizzes_for_giveaways,giveaway_allowed_participants_modes`,
      dataRequest: !this.isNew ? {
        send: () => {
          const params = {
            with: 'channel,quiz.config,prizes,style_config,multimedia_config',
          };

          return this._giveawayHttpS.getOne(this.giveawayId, params);
        },
        body: 'giveaway',
        success: () => {
          let values = {
            ...this.form.dataRequest.body()?.raw,
            channel: this.form.dataRequest.body()?.raw.quiz?.channel,
          }

          this.form.set(values);
          this.participants?.set(this.form.dataRequest.body()?.raw.participants);
          this.blackList?.set(this.form.dataRequest.body()?.raw.black_list || []);

          if (this.form.dataRequest.body()?.raw.participants_excel) {
            this.form.group.get('use_quiz')?.setValue(false);
          }

          if (this.form.dataRequest.body()?.data.status !== 'Pending') {
            this.form.group.disable();
          }
        },
      } : undefined,
      request: {
        send: () => {
          const input = {
            ...this.form.group.getRawValue(),
            black_list: this.blackList(),
          };

          return this.isNew
            ? this._giveawayHttpS.create(input)
            : this._giveawayHttpS.update(this.giveawayId, input);
        },
        success: (res) => {
          if (this.isNew) {
            this._router.navigate(['/sorteos']);
          } else {
            this.form.dataRequest.run();
          }
        },
        notify: true,
      },
    });
  }

  /* -------------------- */

  canResolveParticipants(): boolean {
    return (this.quiz() && this.form.getControl('allowed_participants_mode')?.value)
      || this.form.getControl('participants_excel')?.value;
  }

  addParticipantToBlacklist(participant: any) {
    this.blackList.set([...this.blackList(), participant.id]);
  }

  removeParticipantOfBlacklist(participant: any) {
    this.blackList.set(this.blackList().filter(item => item !== participant.id));
  }

  participantsTableColumns(): any[] {
    const first = this.participants()[0];
    if (!first?.custom_fields?.length) return [];

    const sortStates = this.sortStates();

    return first.custom_fields.map((field: any, index: number) => ({
      title: field.label,
      width: '200px',
      sortOrder: sortStates[field.label] || null,
      priority: index + 1,
      sortFn: (a: any, b: any) => {
        const valA = a.custom_fields.find((f: any) => f.label === field.label)?.value ?? '';
        const valB = b.custom_fields.find((f: any) => f.label === field.label)?.value ?? '';

        // comparar números si aplica
        const numA = parseFloat(valA);
        const numB = parseFloat(valB);
        if (!isNaN(numA) && !isNaN(numB)) return numA - numB;

        // comparación string
        return String(valA).localeCompare(String(valB));
      },
      sortDirections: ['ascend', 'descend', null]
    }));
  }

  // Método para manejar cambios de ordenamiento
  onSortChange(sortName: string, sortValue: string | null): void {
    // Resetear todos los otros ordenamientos
    const newSortStates: {[key: string]: 'ascend' | 'descend' | null} = {};

    // Solo mantener el ordenamiento actual
    if (sortValue === 'ascend' || sortValue === 'descend') {
      newSortStates[sortName] = sortValue;
    }

    this.sortStates.set(newSortStates);
  }
}
