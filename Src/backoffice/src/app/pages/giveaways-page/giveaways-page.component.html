<app-section-title [reloadFn]="list.refresh">
  Sorteos

  <ng-template #extras>
    <a class="btn btn-primary" routerLink="/sorteos/crear">
      <span nz-icon nzType="plus-circle" nzTheme="outline" class="me-1"></span>
      Crear sorteo
    </a>
  </ng-template>
</app-section-title>

<div class="container-fluid">
  <app-filters [list]="list" [formGroup]="list.filters.group">
    <div class="row">
      <nz-form-item class="col-60 col-lg-30 col-xl-15">
        <nz-form-control>
          <nz-form-label>Nombre</nz-form-label>
          <input nz-input formControlName="name" placeholder="Ingresar nombre a buscar">
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="col-60 col-lg-30 col-xl-15">
        <nz-form-control>
          <nz-form-label>Canal</nz-form-label>
          <nz-select formControlName="channels" [compareWith]="list.filters.compareFn" nzMode="multiple" nzAllowClear [nzShowArrow]="true" nzPlaceHolder="Elegir canal">
            <ng-container *ngFor="let channel of list.filters.getCombo('channels_for_giveaways')">
              <nz-option [nzValue]="channel.id" [nzLabel]="channel.name + ' (' + channel.slug + ')'"></nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="col-60 col-lg-30 col-xl-15">
        <nz-form-control>
          <nz-form-label>Partida</nz-form-label>
          <nz-select formControlName="quizzes" [compareWith]="list.filters.compareFn" nzMode="multiple" nzAllowClear [nzShowArrow]="true" nzPlaceHolder="Elegir partida">
            <ng-container *ngFor="let quiz of list.filters.getCombo('quizzes_for_giveaways')">
              <nz-option [nzValue]="quiz.id" [nzLabel]="quiz.name"></nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="col-60 col-lg-30 col-xl-15">
        <nz-form-control>
          <nz-form-label>Estado</nz-form-label>
          <nz-select formControlName="statuses" [compareWith]="list.filters.compareFn" nzMode="multiple" nzAllowClear [nzShowArrow]="true" nzPlaceHolder="Elegir estado">
            <ng-container *ngFor="let status of list.filters.getCombo('giveaway_statuses')">
              <nz-option [nzValue]="status.name" [nzLabel]="status.value.label"></nz-option>
            </ng-container>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <ng-template #moreFilters>
        <div class="row">
          <nz-form-item class="col-60 col-lg-30">
            <nz-form-control>
              <nz-form-label>Creado</nz-form-label>
              <nz-range-picker formControlName="createdAt" nzFormat="longDate"></nz-range-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </ng-template>
    </div>
  </app-filters>

  <div class="box">
    <app-request [requests]="list.getRequests()">
      <nz-table
        nzPaginationType="small"
        [nzScroll]="{ x: '1900px' }"
        [nzFrontPagination]="false"
        [nzData]="list.data()"
        [nzTotal]="list.total()"
        [nzShowTotal]="paginationRangeTpl"
        [nzPageSize]="list.limit()"
        [nzPageIndex]="list.currentPage()"
        (nzQueryParams)="list.onNzQueryParamsChange($event)"
        (nzPageSizeChange)="list.onNzPageSizeChange($event)"
      >
        <thead>
          <tr>
            <th nzWidth="auto" nzColumnKey="name" [nzSortFn]="true">Nombre</th>
            <th nzWidth="200px">Canal</th>
            <th nzWidth="200px">Partida</th>
            <th nzWidth="100px">Ver</th>
            <th nzWidth="120px">Pin</th>
            <th nzWidth="200px">Participantes permitidos</th>
            <th nzWidth="200px">Cantidad de ganadores</th>
            <th nzWidth="140px" nzColumnKey="status" [nzSortFn]="true">Estado</th>
            <th nzWidth="200px" nzColumnKey="created_at" [nzSortFn]="true">Creado</th>
            <th nzWidth="170px" nzAlign="center" nzRight="0">Acciones</th>
            <th nzWidth="0px"></th>
          </tr>
        </thead>

        <tbody>
          <ng-container *ngFor="let item of list.data(); index as i;">
            <tr>
              <td>
                {{ item.data.name }}
              </td>

              <td>
                {{ item.data.channel?.data?.name || '-' }}
              </td>

              <td>
                {{ item.data.quiz?.data?.name || '-' }}
              </td>

              <td>
                <a class="app-fs-18 mx-1"
                  nz-tooltip nzTooltipTitle="Ir al sorteo"
                  [href]="[item.data.monitorUrl]"
                  target="_blank"
                >
                  <span class="text-primary" nz-icon nzType="play-square" nzTheme="outline"></span>
                </a>
              </td>

              <td>
                <span nz-typography nzCopyable [nzCopyText]="item.data.monitorPin">{{ item.data.monitorPin }}</span>
              </td>

              <td>
                {{ item.data.allowedParticipantsModeEnum?.label || '-' }}
              </td>

              <td>
                {{ item.data.winnersCount || '-' }}
              </td>

              <td>
                <nz-tag [nzColor]="item.data.statusEnum.color">
                  {{ item.data.statusEnum.label }}
                </nz-tag>
              </td>

              <td>
                {{ item.data.createdAt.toString() | date:'longDate' }}<br>
                <small class="text-muted">{{ item.data.createdAt.toString() | date:'HH:mm:ss' }}</small>
              </td>

              <td nzAlign="center" nzRight="0">
                <app-giveaway-actions [giveaway]="item" [request]="list.actionRequest" (onSuccess$)="onActionsSuccess($event)"></app-giveaway-actions>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </nz-table>

      <ng-template #paginationRangeTpl let-range="range" let-total>
        {{ range[0] | number }}-{{ range[1] | number  }} de {{ total | number  }} registros
      </ng-template>
    </app-request>
  </div>
</div>
