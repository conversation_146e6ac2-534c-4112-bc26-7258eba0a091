import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { SectionTitleComponent } from 'src/app/components/layouts/section-title/section-title/section-title.component';
import { RouterModule } from '@angular/router';
import { List } from 'src/app/utils/list/list';
import { AuthService } from 'src/app/services/auth.service';
import { FormBuilder } from '@angular/forms';
import { Form } from 'src/app/utils/form/form';
import { stringToObject, mappers } from 'src/app/helper';
import { FiltersComponent } from 'src/app/components/layouts/filters/filters.component';
import { ListModule } from 'src/app/utils/list/list.module';
import { FormModule } from 'src/app/utils/form/form.module';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';
import { GiveawayActionsComponent, GiveawayActionsSuccessEvent } from 'src/app/components/core/giveaway/giveaway-actions/giveaway-actions.component';

@Component({
  selector: 'app-giveaways-page',
  standalone: true,
  imports: [
    SharedModule,
    RouterModule,
    RequestComponent,
    ListModule,
    FormModule,
    FiltersComponent,
    SectionTitleComponent,
    NzBadgeModule,
    GiveawayActionsComponent,
  ],
  templateUrl: './giveaways-page.component.html',
  styleUrls: ['./giveaways-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class GiveawaysPageComponent {

  list: List<any>;

  constructor(
    public authS: AuthService,
    private _giveawayHttpS: GiveawayHttpService,
    private _fb: FormBuilder,
  ) {

    this.list = new List('LaravelPage', {
      persistOnUrl: true,
      request: {
        send: (page: number = 1, reset?: boolean) => {
          if (reset) this.list.reset();

          const filters: any = {
            'filters.name|-like-': this.list.filters.getValue('name'),
            'filters.quiz|has.filters.id|in': this.list.filters.getValue('quizzes').join(','),
            'filters.created_at|between': mappers.betweenDates(this.list.filters.getValue('createdAt'), true),
            'filters.status|in': this.list.filters.getValue('statuses').join(','),
          };

          const params = {
            page,
            limit: this.list.limit(),
            sort: this.list.sort() || '-created_at',
            with: 'quiz',
            ...stringToObject(filters, true),
          };

          return this._giveawayHttpS.getList(params);
        },
        body: 'giveaways',
        success: () => this.list.set(this.list.request.body()),
      },
      filters: new Form(this._fb.group({
        name: [''],
        quizzes: [[]],
        createdAt: [[]],
        statuses: [[]],
      }), {
        combos: `quizzes_for_giveaways,giveaway_statuses`,
      }),
    });
  }

  /* -------------------- */

  onActionsSuccess(event: GiveawayActionsSuccessEvent) {
    this.list.refresh();
  }
}
