import { Injectable } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { GUARD, MAP } from 'src/app/interceptors/contexts';
import { parseQueryParams } from 'src/app/helper';
import { environment } from 'src/environments/environment';
import { Giveaway } from 'src/app/commons/models/giveaway';

@Injectable({
  providedIn: 'root'
})
export class GiveawayHttpService {

  constructor(
    private _httpClient: HttpClient,
  ) { }

  /* -------------------- */

  getList = (params: any) => {
    const queryParams = parseQueryParams(params);

    return this._httpClient.get(`${environment.backendUrl}/api/v1/backoffice/giveaways?${queryParams}`, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
        .set(MAP, res => {
          res.body.giveaways.data = res.body.giveaways.data.map((item: any) => new Giveaway(item, 'backend'));
          return res.body;
        })
    });
  }

  getOne = (giveawayId: number, params: any) => {
    const queryParams = parseQueryParams(params);

    return this._httpClient.get(`${environment.backendUrl}/api/v1/backoffice/giveaways/${giveawayId}?${queryParams}`, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
        .set(MAP, res => {
          res.body.giveaway = new Giveaway(res.body.giveaway, 'backend');
          return res.body;
        })
    });
  }

  create = (input: any) => {
    return this._httpClient.post(`${environment.backendUrl}/api/v1/backoffice/giveaways`, input, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
        .set(MAP, res => {
          res.body.giveaway = new Giveaway(res.body.giveaway, 'backend');
          return res.body;
        })
    });
  }

  update = (giveawayId: number, input: any) => {
    return this._httpClient.put(`${environment.backendUrl}/api/v1/backoffice/giveaways/${giveawayId}`, input, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
        .set(MAP, res => {
          res.body.giveaway = new Giveaway(res.body.giveaway, 'backend');
          return res.body;
        })
    });
  }

  delete = (giveawayId: number) => {
    return this._httpClient.delete(`${environment.backendUrl}/api/v1/backoffice/giveaways/${giveawayId}`, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
    });
  }

  importParticipants = (input: any) => {
    return this._httpClient.post(`${environment.backendUrl}/api/v1/backoffice/giveaways/import-participants`, input, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
    });
  }

  resolveParticipants = (input: any) => {
    return this._httpClient.post(`${environment.backendUrl}/api/v1/backoffice/giveaways/resolve-participants`, input, {
      context: new HttpContext()
        .set(GUARD, 'systemUser')
    });
  }
}
