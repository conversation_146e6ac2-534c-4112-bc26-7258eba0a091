import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { SystemUser } from "./system-user";
import { Question } from "./question";

export interface IQuestionSet {
  id: number,
  name: string,
  isPublic: boolean,
  questions: Question[],
  owner?: SystemUser,
  status: 'Active' | 'Inactive',
  statusEnum: {
    color: string,
    label: string
  },
  quizzesCount?: number,
  quizzesNotStartedCount?: number,
  createdAt: Moment,
  updatedAt: Moment,
  deletedAt?: Moment,
}

export class QuestionSet extends BaseModel<IQuestionSet> {

  /* -------------------- */

  parseFromBackend(data: any): IQuestionSet {
    return {
      ...data,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      deletedAt: data.deletedAt ? moment(data.deletedAt) : null,
      owner: data.owner ? new SystemUser(data.owner, 'backend') : null,
      questions: data.questions?.length ? data.questions.map((item: any) => new Question(item, 'backend')) : [],
    };
  }
}
