import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { SystemUser } from "./system-user";
import { ChannelQuizConfig } from "./channel-quiz-config";

export interface IChannel {
  id: number,
  name: string,
  slug: string,
  url: string,
  qrUrl: string,
  webappTycUrl: string,
  websiteUrl: string,
  monitorUrl: string,
  tycUrl: string,
  email: string,
  status: 'Active' | 'Inactive',
  statusEnum: {
    color: string,
    label: string
  },
  lang: 'Es' | 'En',
  langEnum: {
    label: string,
  },
  config: ChannelQuizConfig,
  systemUser?: SystemUser,
  isProtected: boolean,
  quizzesCount?: number,
  createdAt: Moment,
  updatedAt: Moment,
  deletedAt?: Moment,
}

export class Channel extends BaseModel<IChannel> {

  /* -------------------- */

  parseFromBackend(data: any): IChannel {
    return {
      ...data,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      deletedAt: data.deletedAt ? moment(data.deletedAt) : null,
      config: new ChannelQuizConfig(data.config),
      systemUser: data.systemUser ? new SystemUser(data.systemUser, 'backend') : null,
    };
  }
}
