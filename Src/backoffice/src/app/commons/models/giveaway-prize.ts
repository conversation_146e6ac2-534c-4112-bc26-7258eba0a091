import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Giveaway } from "./giveaway";

export interface IGiveawayPrize {
  id: number,
  name: string,
  description: string,
  quantity: number,
  minPosition?: number,
  maxPosition?: number,
  giveaway: Giveaway,
  image?: any,
  createdAt: Moment,
  updatedAt: Moment,
}

export class GiveawayPrize extends BaseModel<IGiveawayPrize> {

  /* -------------------- */

  parseFromBackend(data: any): IGiveawayPrize {
    return {
      ...data,
      giveaway: new Giveaway(data.giveaway, 'backend'),
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
    };
  }
}
