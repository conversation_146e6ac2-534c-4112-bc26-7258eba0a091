import { Moment } from "moment";
import { BaseModel } from "./base-model";
import { RegisterConfigFieldValue } from "./register-config-field-value";
import { UserQuiz } from "./user-quiz";
import * as moment from "moment";
import { UserQuizProgress } from "./user-quiz-progress";
import { Quiz } from "./quiz";
import { Ranking } from "./ranking";

export interface IUserQuizProfile {
  id: string,
  quiz: Quiz,
  quizId: number,
  quizName: string,
  channelId: number,
  channelName: string,
  userQuiz: UserQuiz,
  lastProgress?: UserQuizProgress,
  endEmail: string,
  registerFieldValues: RegisterConfigFieldValue[],
  name: string,
  email?: string,
  dni?: string,
  group?: {
    id: number,
    name: string,
  },
  quizRanking?: Ranking,
  createdAt: Moment,
}

export class UserQuizProfile extends BaseModel<IUserQuizProfile> {

  getPointsOnQuiz() {
    return this.data.group
      ? this.data.quizRanking?.getItemByGroup(this.data?.group?.id)?.data?.points
      : this.data.lastProgress?.data?.pointsNow ?? 0;
  }

  getPositionOnQuiz() {
    return this.data.group
      ? this.data.quizRanking?.getItemByGroup(this.data?.group?.id)?.data?.position
      : this.data.quizRanking?.getItemByUser(this.data?.id)?.data?.position;
  }

  /* -------------------- */

  parseFromBackend(data: any): IUserQuizProfile {
    return {
      ...data,
      registerFieldValues: data.registerFieldValues?.length ? data.registerFieldValues.map((item: any) => new RegisterConfigFieldValue(item, 'backend')) : [],
      quiz: new Quiz(data.quiz, 'backend'),
      userQuiz: new UserQuiz(data.userQuiz, 'backend'),
      lastProgress: data.lastProgress ? new UserQuizProgress(data.lastProgress, 'backend') : undefined,
      quizRanking: data.quizRanking ? new Ranking(data.quizRanking, 'backend') : undefined,
      createdAt: moment(data.createdAt),
    };
  }
}
