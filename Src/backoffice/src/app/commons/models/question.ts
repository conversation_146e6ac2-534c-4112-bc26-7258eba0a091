import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { SystemUser } from "./system-user";
import { Answer } from "./answer";

export interface IQuestion {
  id: number,
  body: string,
  image: string,
  audio: string,
  isPublic: boolean,
  status: 'Active' | 'Inactive',
  statusEnum: {
    color: string,
    label: string
  },
  lang: 'Es' | 'En',
  langEnum: {
    label: string,
  },
  type: 'Normal' | 'Double' | 'Bomb' | 'Survey',
  typeEnum: {
    label: string,
    multiplier: number,
  },
  owner?: SystemUser,
  answers: Answer[],
  createdAt: Moment,
  updatedAt: Moment,
  deletedAt?: Moment,
}

export class Question extends BaseModel<IQuestion> {

  /* -------------------- */

  parseFromBackend(data: any): IQuestion {
    return {
      ...data,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      deletedAt: data.deletedAt ? moment(data.deletedAt) : null,
      systemUser: data.systemUser ? new SystemUser(data.systemUser, 'backend') : null,
      answers: data.answers?.length ? data.answers.map((item: any) => new Answer(item, 'backend')) : [],
    };
  }
}
