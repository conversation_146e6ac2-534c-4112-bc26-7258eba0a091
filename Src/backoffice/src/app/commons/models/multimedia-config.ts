import { BaseModel } from "./base-model";
import { Media } from "./media";

export interface IMultimediaConfig {
  id: number,
  logo?: Media,
  logoLink?: string,
  bgImage?: Media,
  bgVideo?: Media,
  monitorBgImage?: Media,
  monitorBgVideo?: Media,
  banner?: Media,
  bannerLink?: string,
}

export class MultimediaConfig extends BaseModel<IMultimediaConfig> {

  parseFromBackend(data: any): IMultimediaConfig {
    return {
      ...data,
      logo: data.logo ? new Media(data.logo) : null,
      bgImage: data.bgImage ? new Media(data.bgImage) : null,
      bgVideo: data.bgVideo ? new Media(data.bgVideo) : null,
      monitorBgImage: data.monitorBgImage ? new Media(data.monitorBgImage) : null,
      monitorBgVideo: data.monitorBgVideo ? new Media(data.monitorBgVideo) : null,
      banner: data.banner ? new Media(data.banner) : null,
    };
  }
}
