import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { IAuth } from "src/app/services/auth.service";
import { Channel } from "./channel";
import { isArray } from "lodash";

export interface ISystemUser {
  id: number,
  firstName: string,
  lastName: string,
  fullName: string,
  email: string,
  emailVerifiedAt?: Moment,
  picture: string,
  socialId: string,
  socialDriver: 'Google' | 'Facebook',
  socialAvatar: string,
  type: 'Administrator' | 'Client' | 'ClientChild',
  typeEnum: {
    color: string,
    label: string
  },
  status: 'Active' | 'Inactive',
  statusEnum: {
    color: string,
    label: string
  },
  lang: 'Es' | 'En',
  langEnum: {
    label: string,
  },
  parent?: SystemUser,
  rolesAndPermissions: {
    roles: string[],
    permissions: string[],
  },
  hasActiveChannels: boolean,
  channel: Channel,
  channels: Channel[],
  createdAt: Moment,
  updatedAt: Moment,
  deletedAt?: Moment,
}

export class SystemUser extends BaseModel<ISystemUser> {

  /* -------------------- */

  is(type: ISystemUser['type']): boolean {
    return type === this.data.type;
  }

  hasRole(roles: string | string[], matchAll: boolean = true): boolean {
    if (!isArray(roles)) roles = [roles];
    return roles[matchAll ? 'every' : 'some'](role => this.data.rolesAndPermissions.roles.includes(role));
  }

  can(permissions: string | string[], matchAll: boolean = true): boolean {
    if (!isArray(permissions)) permissions = [permissions];
    return permissions[matchAll ? 'every' : 'some'](permission => this.data.rolesAndPermissions.permissions?.includes(permission));
  }

  /* -------------------- */

  parseFromBackend(data: any): ISystemUser {
    return {
      ...data,
      emailVerifiedAt: data.emailVerifiedAt ? moment(data.emailVerifiedAt) : null,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      deletedAt: data.deletedAt ? moment(data.deletedAt) : null,
      parent: data.parent ? new SystemUser(data.parent, 'backend') : null,
      channel: data.channel ? new Channel(data.channel, 'backend') : null,
      channels: data.channels?.length ? data.channels.map((item: any) => new Channel(item, 'backend')) : [],
    };
  }

  /* -------------------- */

  parseForAuthData(): IAuth['data'] {
    return {
      id: this.data.id,
      email: this.data.email,
      name: this.data.fullName,
      isVerified: !!this.data.emailVerifiedAt,
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      picture: this.data.picture,
      roles: this.data.rolesAndPermissions.roles,
      permissions: this.data.rolesAndPermissions.permissions,
      model: this,
    };
  }
}
