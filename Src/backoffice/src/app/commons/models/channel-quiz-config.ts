import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { MultimediaConfig } from "./multimedia-config";
import { StyleConfig } from "./style-config";
import { PrizeConfig } from "./prize-config";
import { RegisterConfigField } from "./register-config-field";

export interface IChannelQuizConfig {
  id: number,
  maxUsersPerQuiz: number,
  timePerQuestion: number,
  messagePrimary?: string,
  messageSecondary?: string,
  allowNegativePoints: boolean,
  useMonitor: boolean,
  useRandomQuestions: boolean,
  showRandomBetweenQuestions: boolean,
  timePerRanking?: number,
  showCorrectAnswer: boolean,
  playAudioOnMonitor: boolean,
  askForEmailAtTheEnd: string,
  multimediaConfig?: MultimediaConfig,
  styleConfig?: StyleConfig,
  prizeConfigs: PrizeConfig[],
  registerConfigFields: RegisterConfigField[],
  whitelist: any,
  scoringMode: string,
  createdAt: Moment,
  updatedAt: Moment,
}

export class ChannelQuizConfig extends BaseModel<IChannelQuizConfig> {

  /* -------------------- */

  parseFromBackend(data: any): IChannelQuizConfig {
    return {
      ...data,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      multimediaConfig: data.multimediaConfig ? new MultimediaConfig(data.multimediaConfig, 'backend') : undefined,
      styleConfig: data.styleConfig ? new StyleConfig(data.styleConfig, 'backend') : undefined,
      prizeConfigs: data.prizeConfigs?.length ? data.prizeConfigs.map((item: any) => new PrizeConfig(item, 'backend')) : [],
      registerConfigFields: data.registerConfigFields?.length ? data.registerConfigFields.map((item: any) => new RegisterConfigField(item, 'backend')) : [],
    };
  }
}
