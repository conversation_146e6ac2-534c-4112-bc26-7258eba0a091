import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";

export interface IRankingItem {
  userProfileId: number,
  quizGroupId: number,
  name: string,
  email?: string,
  extraRegisterData?: [
    {
      value: any,
      label: string,
    }
  ],
  customFields?: [
    {
      value: any,
      label: string,
    }
  ],
  progress: any,
  points: number,
  position: number,
  podium: number,
  usersInGroupCount: number,
  createdAt: Moment,
}

export class RankingItem extends BaseModel<IRankingItem> {

  /* -------------------- */

  inPodium(): boolean {
    return this.data.podium === 0 || this.data.position <= this.data.podium;
  }

  isUser(userProfileId: number): boolean {
    return this.data.userProfileId === userProfileId;
  }

  isGroup(quizGroupId: number | undefined): boolean {
    return this.data.quizGroupId === quizGroupId;
  }

  /* -------------------- */

  parseFromBackend(data: any): IRankingItem {
    return {
      ...data,
      createdAt: moment(data.createdAt),
    };
  }
}
