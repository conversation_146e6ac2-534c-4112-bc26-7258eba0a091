import { BaseModel } from "./base-model";
import { Quiz } from "./quiz";
import { UserQuizProgress } from "./user-quiz-progress";

export interface IUserQuiz {
  id: number,
  quiz: Quiz,
  progress?: UserQuizProgress[],
  lastProgress?: UserQuizProgress,
}

export class UserQuiz extends BaseModel<IUserQuiz> {

  parseFromBackend(data: any): IUserQuiz {
    return {
      ...data,
      quiz: new Quiz(data.quiz, 'backend'),
      progress: data.progress ? data.progress.map((item: any) => new UserQuizProgress(item, 'backend')) : [],
      lastProgress: data.lastProgress ? new UserQuizProgress(data.lastProgress, 'backend') : undefined,
    };
  }
}
