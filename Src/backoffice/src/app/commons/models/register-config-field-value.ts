import { BaseModel } from "./base-model";
import { RegisterConfigField } from "./register-config-field";

export interface RegisterConfigFieldValue {
  id: number,
  value: any,
  field: RegisterConfigField,
}

export class RegisterConfigFieldValue extends BaseModel<RegisterConfigFieldValue> {

  parseFromBackend(data: any): RegisterConfigFieldValue {
    return {
      ...data,
      field: new RegisterConfigField(data.field),
    };
  }
}
