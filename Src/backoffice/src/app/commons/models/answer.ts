import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";

export interface IAnswer {
  id: number,
  body: string,
  isCorrect: boolean,
  usersCount: number,
  createdAt: Moment,
  updatedAt: Moment,
}

export class Answer extends BaseModel<IAnswer> {

  /* -------------------- */

  parseFromBackend(data: any): IAnswer {
    return {
      ...data,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
    };
  }
}
