import { BaseModel } from "./base-model";
import { Ranking } from "./ranking";
import { IAnswer } from "./answer";
import { IQuestion } from "./question";
import { RegisterConfigField } from "./register-config-field";
import { Moment } from "moment";
import * as moment from "moment";

export interface IQuizReport {
  channelId: number,
  quizId: number,
  quizTypeEnum: {
    label: string
  },
  quizLastEvent?: {
    type: 'Paused' | 'Playing' | 'Finished',
    typeEnum: {
      label: string
    },
  },
  quizRegisterConfigFields: RegisterConfigField[],
  questionsCount: {
    total: number,
    double: number,
    bomb: number,
    survey: number,
  },
  questions: (IQuestion & {
    answers: (IAnswer & {
      id: number,
      body: string,
      isCorrect: boolean,
      usersCount: number,
      usersCountPercentage: number,
      users: {
        customFields: { key: string, value: any }[],
        points: number,
      }[],
    })[],
    usersCountCorrectPercentage: number,
    usersCountIncorrectPercentage: number,
  })[],
  users: {
    ranking: Ranking,
    count: number,
    maxScore: number,
  },
  useGroups: boolean,
  isMultiple: boolean,
  updatedAt?: Moment,
}

export class QuizReport extends BaseModel<IQuizReport> {

  /* -------------------- */

  parseFromBackend(data: any): IQuizReport {
    const ranking = data.ranking ? new Ranking(data.ranking, 'backend') : undefined;

    return {
      ...data,
      updatedAt: data.updatedAt ? moment(data.updatedAt) : undefined,
      users: {
        ranking: ranking,
        count: ranking?.data.items ? ranking?.data.items.length : undefined,
        maxScore: ranking?.data.items[0] ? ranking?.data.items[0].data.points : undefined,
      },
      quizRegisterConfigFields: data.quizRegisterConfigFields?.length ? data.quizRegisterConfigFields.map((item: any) => new RegisterConfigField(item, 'backend')) : [],
    };
  }
}
