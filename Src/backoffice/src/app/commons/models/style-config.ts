import { BaseModel } from "./base-model";
import { Media } from "./media";

export interface IStyleConfig {
  id: number,
  cssFile?: Media,
  textPrimary?: string,
  textSecondary?: string,
  bgPrimary?: string,
  bgSecondary?: string,
  btnTextPrimary?: string,
  btnTextSecondary?: string,
  btnBgPrimary?: string,
  btnBgSecondary?: string,
  registerLabelText?: string,
  registerInputText?: string,
  progressBarBg?: string,
  questionText?: string,
  questionTextBorder?: string,
  answerBtnText?: string,
  answerBtnBgSelected?: string,
  answerBtnBgCorrect?: string,
  answerBtnBgIncorrect?: string,
  rankingText?: string,
  rankingBg?: string,
  bannerBtnText?: string,
  bannerBtnBg?: string,
}

export class StyleConfig extends BaseModel<IStyleConfig> {

  /* -------------------- */

  parseFromBackend(data: any): IStyleConfig {
    return {
      ...data,
      cssFile: data.cssFile ? new Media(data.cssFile) : null,
    };
  }
}
