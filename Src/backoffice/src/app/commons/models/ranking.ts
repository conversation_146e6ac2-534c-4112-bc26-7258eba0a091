import { BaseModel } from "./base-model";
import { RankingItem } from "./ranking-item";

export interface IRanking {
  items: RankingItem[],
}

export class Ranking extends BaseModel<IRanking> {

  /* -------------------- */

  private _getItem(field: string, value: any) {
    return this.data.items.find(item => (item.data as any)[field] === value);
  }

  getItemByUser(userProfileId: string | undefined) {
    return this._getItem('userProfileId', userProfileId);
  }

  getItemByGroup(quizGroupId: number | undefined) {
    return this._getItem('quizGroupId', quizGroupId);
  }

  getItemByPosition(position: number) {
    return this._getItem('position', position);
  }

  /* -------------------- */

  parseFromBackend(data: any): IRanking {
    return {
      ...data,
      items: data.items.map((item: any) => new RankingItem(item,'backend')),
    };
  }
}
