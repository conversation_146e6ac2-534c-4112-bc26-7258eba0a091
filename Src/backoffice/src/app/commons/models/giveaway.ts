import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Quiz } from "./quiz";
import { GiveawayPrize } from "./giveaway-prize";

export interface IGiveaway {
  id: number,
  name: string,
  monitorPin: string,
  quiz?: Quiz,
  status: 'Pending' | 'InProgress' | 'Completed',
  statusEnum: {
    color: string,
    label: string
  },
  allowedParticipantsMode: 'All' | 'NonWinners' | 'CorrectAnswers',
  allowedParticipantsModeEnum: {
    label: string
  },
  minCorrectAnswers?: number,
  colorScheme?: string,
  audioEnabled: boolean,
  drawDurationSeconds: number,
  participants: any[],
  winners?: any[],
  winnersCount?: number,
  blackList?: any[],
  logo?: any,
  bgVideo?: any,
  monitorBgVideo?: any,
  monitorUrl?: string,
  prizes: GiveawayPrize[],
  createdAt: Moment,
  updatedAt: Moment,
}

export class Giveaway extends BaseModel<IGiveaway> {

  /* -------------------- */

  parseFromBackend(data: any): IGiveaway {
    return {
      ...data,
      quiz: data.quiz ? new Quiz(data.quiz, 'backend') : null,
      prizes: data.prizes?.length ? data.prizes.map((item: any) => new GiveawayPrize(item, 'backend')) : [],
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
    };
  }
}
