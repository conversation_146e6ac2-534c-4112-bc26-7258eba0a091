import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Channel } from "./channel";
import { Ranking } from "./ranking";
import { ChannelQuizConfig } from "./channel-quiz-config";

export interface IQuiz {
  id: number,
  name: string,
  pin: string,
  monitorPin: string,
  startsAt: Moment,
  endsAt?: Moment,
  type: 'Live' | 'OnDemand',
  typeEnum: {
    label: string
  },
  status: 'Active' | 'Inactive',
  statusEnum: {
    color: string,
    label: string
  },
  mode: 'Manual' | 'Scheduled',
  modeEnum: {
    label: string
  },
  channel: Channel,
  config: ChannelQuizConfig,
  ranking?: Ranking,
  url: string,
  qrUrl: string,
  monitorUrl: string,
  monitorRecordViewUrl: string,
  lastEvent?: {
    type: 'Paused' | 'Playing' | 'Finished',
    typeEnum: {
      label: string
    },
  },
  createdAt: Moment,
  updatedAt: Moment,
  deletedAt?: Moment,
}

export class Quiz extends BaseModel<IQuiz> {

  /* -------------------- */

  parseFromBackend(data: any): IQuiz {
    return {
      ...data,
      startsAt: moment(data.startsAt),
      endsAt: data.endsAt ? moment(data.endsAt) : undefined,
      createdAt: moment(data.createdAt),
      updatedAt: moment(data.updatedAt),
      deletedAt: data.deletedAt ? moment(data.deletedAt) : undefined,
      channel: new Channel(data.channel, 'backend'),
      ranking: data.ranking ? new Ranking(data.ranking, 'backend') : undefined,
      config: data.config ? new ChannelQuizConfig(data.config, 'backend') : undefined,
    };
  }
}
