<ng-container *ngIf="{
  useVerifiedCustom: isVerified === true || isVerified === false,
  isVerifiedCustom: isVerified,
  isVerified: authS.systemUser().isVerified(),
  isVerifying: verifyRequest.isLoading()
} as vm">
  <ng-container *ngIf="vm.useVerifiedCustom ? vm.isVerifiedCustom : vm.isVerified; else noVerifiedTpl">
    <ng-content></ng-content>
  </ng-container>

  <ng-template #noVerifiedTpl>
    <ng-container *ngIf="vm.isVerifying && !vm.useVerifiedCustom; else notVerifyingTpl">
      <nz-alert class="mb-4"
        nzType="info"
        [nzMessage]="message"
        [nzDescription]="description"
        nzShowIcon
      >
        <ng-template #message>
          <strong>Verificando cuenta...</strong>
        </ng-template>

        <ng-template #description>
          Estamos verificando tu cuenta. Aguarda unos segundos por favor.
        </ng-template>
      </nz-alert>
    </ng-container>

    <ng-template #notVerifyingTpl>
      <nz-alert class="mb-4"
        nzType="error"
        [nzMessage]="message"
        [nzDescription]="!vm.useVerifiedCustom ? description : null"
        nzShowIcon
      >
        <ng-template #message>
          <strong>Cuenta no verificada</strong>
        </ng-template>

        <ng-template #description>
          <p>Tu cuenta aún no ha sido verificada. Revisa la bandeja de entrada (o la carpeta de SPAM) de tu dirección de correo y verifica tu cuenta para acceder a todos los servicios de Trivias Fun.</p>

          <button [appLoadingBtn]="requestRequest.isLoading()" class="btn btn-outline-primary btn-sm" (click)="requestRequest.run()">
            Reenviar email de verificación
          </button>
        </ng-template>
      </nz-alert>
    </ng-template>
  </ng-template>
</ng-container>
