import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { AuthService } from 'src/app/services/auth.service';
import { SharedModule } from 'src/app/shared.module';

@Component({
  selector: 'app-missing-channel-warning',
  standalone: true,
  imports: [
    SharedModule,
    NzAlertModule,
    RouterModule,
  ],
  templateUrl: './missing-channel-warning.component.html',
  styleUrls: ['./missing-channel-warning.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MissingChannelWarningComponent {

  constructor(
    public authS: AuthService,
  ) {}
}
