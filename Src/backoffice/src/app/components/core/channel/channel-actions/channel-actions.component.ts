import { ChangeDetectionStrategy, Component, EventEmitter, Injector, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { RouteService } from 'src/app/services/route.service';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { Request } from 'src/app/utils/request/request';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { Channel } from 'src/app/commons/models/channel';
import { ChannelHttpService } from 'src/app/services/http/channel-http.service';

export interface ChannelActionsSuccessEvent {
  action:
    | 'activate'
    | 'deactivate'
    | 'delete'
  ,
  data?: any,
}

@Component({
  selector: 'app-channel-actions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzIconModule,
    NzDropDownModule,
  ],
  templateUrl: './channel-actions.component.html',
  styleUrls: ['./channel-actions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChannelActionsComponent implements OnInit {

  @Input({ required: true }) channel!: Channel | null;
  @Input() type: 'buttons' | 'dropdown' = 'buttons';
  @Input() notify = false;
  @Input() request = new Request();

  @Output() onSuccess$: EventEmitter<ChannelActionsSuccessEvent> = new EventEmitter();

  activateRequest!: Request;
  deactivateRequest!: Request;
  deleteRequest!: Request;

  private _injector = inject(Injector);

  constructor(
    private _authS: AuthService,
    private _channelHttpS: ChannelHttpService,
    private _routeS: RouteService,
  ) { }

  ngOnInit(): void {
    this.activateRequest = new Request({
      send: () => this._channelHttpS.activate(this.channel?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'activate', data: res.body.channel }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deactivateRequest = new Request({
      send: () => this._channelHttpS.deactivate(this.channel?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'activate', data: res.body.channel }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deleteRequest = new Request({
      send: () => this._channelHttpS.delete(this.channel?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'delete', data: res.body.channel }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });
  }

  /* -------------------- */

  canShowActions(): boolean {
    return this.canShowToggleStatus()
      || this.canShowEdit()
      || this.canShowDelete();
  }

  canShowToggleStatus(): boolean {
    return this._authS.systemUser().can(['ChannelActivate', 'ChannelDeactivate']) && !this.channel?.data.isProtected;
  }

  canShowEdit(): boolean {
    return this._routeS.currentPage().name !== 'ChannelSavePage';
  }

  canShowDelete(): boolean {
    return this._authS.systemUser().can('ChannelDelete') && !this.channel?.data.isProtected;
  }
}
