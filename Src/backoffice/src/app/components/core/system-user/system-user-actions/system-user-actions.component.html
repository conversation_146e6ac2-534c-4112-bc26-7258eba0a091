<ng-container *ngIf="systemUser">
  <ng-container *ngIf="type === 'buttons'">
    <ng-container *ngIf="canShowToggleStatus()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip [nzTooltipTitle]="systemUser.data.status === 'Active' ? 'Desactivar' : 'Activar'"
        (click)="systemUser.data.status === 'Active' ? deactivateRequest.run() : activateRequest.run()"
      >
        <span class="text-primary" nz-icon [nzType]="systemUser.data.status === 'Active' ? 'stop' : 'check'" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowEdit()">
      <a class="app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Editar"
        [routerLink]="['/usuarios', systemUser.data.id]"
      >
        <span class="text-primary" nz-icon nzType="edit" nzTheme="outline"></span>
      </a>
    </ng-container>

    <ng-container *ngIf="canShowDelete()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Borrar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro de eliminar al usuario ' + systemUser.data.fullName + '?'"
        (nzOnConfirm)="deleteRequest.run()"
      >
        <span class="text-danger" nz-icon nzType="delete" nzTheme="outline"></span>
      </button>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="type === 'dropdown' && canShowActions()">
    <button class="btn-styless app-fs-18 mx-1"
      nz-dropdown
      [nzDropdownMenu]="actionsMenu"
    >
      <span nz-icon nzType="more" nzTheme="outline"></span>
    </button>
    <nz-dropdown-menu #actionsMenu="nzDropdownMenu">
      <ul nz-menu>
        <li nz-menu-item class="text-primary app-fs-16">
          <strong>Acciones</strong>
        </li>
        <li nz-menu-divider></li>
        <li *ngIf="canShowToggleStatus()" nz-menu-item>
          <button *ngIf="systemUser.data.status === 'Active'" class="btn-styless" (click)="deactivateRequest.run()">Desactivar</button>
          <button *ngIf="systemUser.data.status !== 'Active'" class="btn-styless" (click)="activateRequest.run()">Activar</button>
        </li>
        <li *ngIf="canShowEdit()" nz-menu-item>
          <a class="link-styless" [routerLink]="['/usuarios', systemUser.data.id]">
            Editar
          </a>
        </li>
        <li *ngIf="canShowDelete()" nz-menu-item>
          <button class="btn-styless text-danger"
            nz-popconfirm
            [nzPopconfirmTitle]="'¿Estás seguro de eliminar al usuario ' + systemUser.data.fullName + '?'"
            (nzOnConfirm)="deleteRequest.run()"
          >
            Borrar
          </button>
        </li>
      </ul>
    </nz-dropdown-menu>
  </ng-container>
</ng-container>
