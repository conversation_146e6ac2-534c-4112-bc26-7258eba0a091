<ng-container *ngIf="questionSet">
  <ng-container *ngIf="type === 'buttons'">
    <ng-container *ngIf="canShowToggleStatus()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip [nzTooltipTitle]="questionSet.data.status === 'Active' ? 'Desactivar' : 'Activar'"
        (click)="questionSet.data.status === 'Active' ? deactivateRequest.run() : activateRequest.run()"
      >
        <span class="text-primary" nz-icon [nzType]="questionSet.data.status === 'Active' ? 'stop' : 'check'" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowEdit()">
      <a class="app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Editar"
        [routerLink]="['/sets-de-preguntas', questionSet.data.id]"
      >
        <span class="text-primary" nz-icon nzType="edit" nzTheme="outline"></span>
      </a>
    </ng-container>

    <ng-container *ngIf="canShowDuplicate()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Duplicar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro que querés duplicar el set de preguntas ' + questionSet.data.name + '?'"
        (nzOnConfirm)="duplicateRequest.run()"
      >
        <span class="text-primary" nz-icon nzType="copy" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowDelete()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Borrar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro de eliminar al set de preguntas ' + questionSet.data.name + '?'"
        (nzOnConfirm)="deleteRequest.run()"
      >
        <span class="text-danger" nz-icon nzType="delete" nzTheme="outline"></span>
      </button>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="type === 'dropdown' && canShowActions()">
    <button class="btn-styless app-fs-18 mx-1"
      nz-dropdown
      [nzDropdownMenu]="actionsMenu"
    >
      <span nz-icon nzType="more" nzTheme="outline"></span>
    </button>
    <nz-dropdown-menu #actionsMenu="nzDropdownMenu">
      <ul nz-menu>
        <li nz-menu-item class="text-primary app-fs-16">
          <strong>Acciones</strong>
        </li>
        <li nz-menu-divider></li>
        <li *ngIf="canShowToggleStatus()" nz-menu-item>
          <button *ngIf="questionSet.data.status === 'Active'" class="btn-styless" (click)="deactivateRequest.run()">Desactivar</button>
          <button *ngIf="questionSet.data.status !== 'Active'" class="btn-styless" (click)="activateRequest.run()">Activar</button>
        </li>
        <li *ngIf="canShowEdit()" nz-menu-item>
          <a class="link-styless" [routerLink]="['/sets-de-preguntas', questionSet.data.id]">
            Editar
          </a>
        </li>
        <li *ngIf="canShowDelete()" nz-menu-item>
          <button class="btn-styless text-danger"
            nz-popconfirm
            [nzPopconfirmTitle]="'¿Estás seguro de eliminar al set de preguntas ' + questionSet.data.name + '?'"
            (nzOnConfirm)="deleteRequest.run()"
          >
            Borrar
          </button>
        </li>
      </ul>
    </nz-dropdown-menu>
  </ng-container>
</ng-container>
