import { ChangeDetectionStrategy, Component, EventEmitter, Injector, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { RouteService } from 'src/app/services/route.service';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { Request } from 'src/app/utils/request/request';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { QuestionSet } from 'src/app/commons/models/question-set';
import { QuestionSetHttpService } from 'src/app/services/http/question-set-http.service';

export interface QuestionSetActionsSuccessEvent {
  action:
    | 'activate'
    | 'deactivate'
    | 'delete'
    | 'duplicate'
  ,
  data?: any,
}

@Component({
  selector: 'app-question-set-actions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzIconModule,
    NzDropDownModule,
  ],
  templateUrl: './question-set-actions.component.html',
  styleUrls: ['./question-set-actions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QuestionSetActionsComponent implements OnInit {

  @Input({ required: true }) questionSet!: QuestionSet | null;
  @Input() type: 'buttons' | 'dropdown' = 'buttons';
  @Input() notify = false;
  @Input() request = new Request();

  @Output() onSuccess$: EventEmitter<QuestionSetActionsSuccessEvent> = new EventEmitter();

  activateRequest!: Request;
  deactivateRequest!: Request;
  deleteRequest!: Request;
  duplicateRequest!: Request;

  private _injector = inject(Injector);

  constructor(
    private _authS: AuthService,
    private _questionSetHttpS: QuestionSetHttpService,
    private _routeS: RouteService,
  ) { }

  ngOnInit(): void {
    this.activateRequest = new Request({
      send: () => this._questionSetHttpS.activate(this.questionSet?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'activate', data: res.body.question_set }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deactivateRequest = new Request({
      send: () => this._questionSetHttpS.deactivate(this.questionSet?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'activate', data: res.body.question_set }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.duplicateRequest = new Request({
      send: () => this._questionSetHttpS.duplicate(this.questionSet?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'duplicate', data: res.body.question_set }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deleteRequest = new Request({
      send: () => this._questionSetHttpS.delete(this.questionSet?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'delete', data: res.body.question_set }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });
  }

  /* -------------------- */

  canShowActions(): boolean {
    return this.canShowToggleStatus()
      || this.canShowEdit()
      || this.canShowDelete()
      || this.canShowDuplicate();
  }

  canShowDuplicate(): boolean {
    return this._authS.systemUser().can('QuestionSetCreate');
  }

  canShowToggleStatus(): boolean {
    if (this._authS.systemUser().model()?.is('Client')) {
      return this._authS.systemUser().can(['QuestionSetActivate', 'QuestionSetDeactivate']) && this.authSystemUserIsOwner();
    }

    return this._authS.systemUser().can(['QuestionSetActivate', 'QuestionSetDeactivate']);
  }

  canShowEdit(): boolean {
    if (this._authS.systemUser().model()?.is('Client')) {
      return this._authS.systemUser().can('QuestionSetUpdate') && this.authSystemUserIsOwner();
    }

    return this._routeS.currentPage.name !== 'QuestionSetSavePage';
  }

  canShowDelete(): boolean {
    if (this._authS.systemUser().model()?.is('Client')) {
      return this._authS.systemUser().can('QuestionSetDelete') && this.authSystemUserIsOwner();
    }

    return this._authS.systemUser().can('QuestionSetDelete');
  }

  /* -------------------- */

  authSystemUserIsOwner(): boolean {
    return this.questionSet?.data.owner?.data.id === this._authS.systemUser().data()?.id;
  }
}
