<ng-container *ngIf="quiz">
  <ng-container *ngIf="type === 'buttons'">
    <ng-container *ngIf="canShowPlayAndPause()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip [nzTooltipTitle]="!quiz.data.lastEvent || quiz.data.lastEvent.type === 'Paused' ? 'Jugar' : 'Pausar'"
        (click)="!quiz.data.lastEvent || quiz.data.lastEvent.type === 'Paused' ? playRequest.run() : pauseRequest.run()"
      >
        <span class="text-primary" nz-icon [nzType]="!quiz.data.lastEvent || quiz.data.lastEvent.type === 'Paused' ? 'play-circle' : 'pause-circle'" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowToggleStatus()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip [nzTooltipTitle]="quiz.data.status === 'Active' ? 'Desactivar' : 'Activar'"
        (click)="quiz.data.status === 'Active' ? deactivateRequest.run() : activateRequest.run()"
      >
        <span class="text-primary" nz-icon [nzType]="quiz.data.status === 'Active' ? 'stop' : 'check'" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowEdit()">
      <a class="app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Editar"
        [routerLink]="['/partidas', quiz.data.id]"
      >
        <span class="text-primary" nz-icon nzType="edit" nzTheme="outline"></span>
      </a>
    </ng-container>

    <ng-container *ngIf="canShowDuplicate()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Duplicar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro que querés duplicar la partida ' + quiz.data.name + '?'"
        (nzOnConfirm)="duplicateRequest.run()"
      >
        <span class="text-primary" nz-icon nzType="copy" nzTheme="outline"></span>
      </button>
    </ng-container>

    <ng-container *ngIf="canShowDelete()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Borrar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro de eliminar la partida ' + quiz.data.name + '?'"
        (nzOnConfirm)="deleteRequest.run()"
      >
        <span class="text-danger" nz-icon nzType="delete" nzTheme="outline"></span>
      </button>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="type === 'dropdown' && canShowActions()">
    <button class="btn-styless app-fs-18 mx-1"
      nz-dropdown
      [nzDropdownMenu]="actionsMenu"
    >
      <span nz-icon nzType="more" nzTheme="outline"></span>
    </button>
    <nz-dropdown-menu #actionsMenu="nzDropdownMenu">
      <ul nz-menu>
        <li nz-menu-item class="text-primary app-fs-16">
          <strong>Acciones</strong>
        </li>
        <li nz-menu-divider></li>
        <li *ngIf="canShowPlayAndPause()" nz-menu-item>
          <button *ngIf="!quiz.data.lastEvent || quiz.data.lastEvent.type === 'Paused'" class="btn-styless" (click)="playRequest.run()">Jugar</button>
          <button *ngIf="quiz.data.lastEvent && quiz.data.lastEvent.type === 'Playing'" class="btn-styless" (click)="pauseRequest.run()">Pausar</button>
        </li>
        <li *ngIf="canShowToggleStatus()" nz-menu-item>
          <button *ngIf="quiz.data.status === 'Active'" class="btn-styless" (click)="deactivateRequest.run()">Desactivar</button>
          <button *ngIf="quiz.data.status !== 'Active'" class="btn-styless" (click)="activateRequest.run()">Activar</button>
        </li>
        <li *ngIf="canShowEdit()" nz-menu-item>
          <a class="link-styless" [routerLink]="['/partidas', quiz.data.id]">
            Editar
          </a>
        </li>
        <li *ngIf="canShowDuplicate()" nz-menu-item>
          <button class="btn-styless"
            nz-popconfirm
            [nzPopconfirmTitle]="'¿Estás seguro que querés duplicar la partida ' + quiz.data.name + '?'"
            (nzOnConfirm)="duplicateRequest.run()"
          >
            Duplicar
          </button>
        </li>
        <li *ngIf="canShowDelete()" nz-menu-item>
          <button class="btn-styless text-danger"
            nz-popconfirm
            [nzPopconfirmTitle]="'¿Estás seguro de eliminar la partida ' + quiz.data.name + '?'"
            (nzOnConfirm)="deleteRequest.run()"
          >
            Borrar
          </button>
        </li>
      </ul>
    </nz-dropdown-menu>
  </ng-container>
</ng-container>
