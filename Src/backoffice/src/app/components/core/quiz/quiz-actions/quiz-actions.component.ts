import { ChangeDetectionStrategy, Component, EventEmitter, Injector, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { RouteService } from 'src/app/services/route.service';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { Request } from 'src/app/utils/request/request';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { Quiz } from 'src/app/commons/models/quiz';
import { QuizHttpService } from 'src/app/services/http/quiz-http.service';

export interface QuizActionsSuccessEvent {
  action:
    | 'activate'
    | 'deactivate'
    | 'duplicate'
    | 'delete'
    | 'play'
    | 'pause'
  ,
  data?: any,
}

@Component({
  selector: 'app-quiz-actions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzIconModule,
    NzDropDownModule,
  ],
  templateUrl: './quiz-actions.component.html',
  styleUrls: ['./quiz-actions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QuizActionsComponent implements OnInit {

  @Input({ required: true }) quiz!: Quiz | null;
  @Input() type: 'buttons' | 'dropdown' = 'buttons';
  @Input() notify = false;
  @Input() request = new Request();

  @Output() onSuccess$: EventEmitter<QuizActionsSuccessEvent> = new EventEmitter();

  activateRequest!: Request;
  deactivateRequest!: Request;
  deleteRequest!: Request;
  duplicateRequest!: Request;
  playRequest!: Request;
  pauseRequest!: Request;

  private _injector = inject(Injector);

  constructor(
    private _authS: AuthService,
    private _quizHttpS: QuizHttpService,
    private _routeS: RouteService,
  ) { }

  ngOnInit(): void {
    this.activateRequest = new Request({
      send: () => this._quizHttpS.activate(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'activate', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deactivateRequest = new Request({
      send: () => this._quizHttpS.deactivate(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'deactivate', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.deleteRequest = new Request({
      send: () => this._quizHttpS.delete(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'delete', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.duplicateRequest = new Request({
      send: () => this._quizHttpS.duplicate(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'duplicate', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    /* -------------------- */

    this.playRequest = new Request({
      send: () => this._quizHttpS.play(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'play', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });

    this.pauseRequest = new Request({
      send: () => this._quizHttpS.pause(this.quiz?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'pause', data: res.body.quiz }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });
  }

  /* -------------------- */

  canShowActions(): boolean {
    return this.canShowToggleStatus()
      || this.canShowEdit()
      || this.canShowDelete();
  }

  canShowToggleStatus(): boolean {
    return this._authS.systemUser().can(['QuizActivate', 'QuizDeactivate']);
  }

  canShowEdit(): boolean {
    return this._routeS.currentPage().name !== 'QuizSavePage';
  }

  canShowDelete(): boolean {
    return this._authS.systemUser().can('QuizDelete');
  }

  canShowDuplicate(): boolean {
    return this._authS.systemUser().can('QuizCreate');
  }

  canShowPlayAndPause(): boolean {
    return this._authS.systemUser().can(['QuizPlay', 'QuizPause']) && this.quiz?.data.type === 'Live' && this.quiz?.data.lastEvent?.type !== 'Finished';
  }
}
