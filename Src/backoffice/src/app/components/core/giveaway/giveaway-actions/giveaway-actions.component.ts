import { ChangeDetectionStrategy, Component, EventEmitter, Injector, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { RouteService } from 'src/app/services/route.service';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { Request } from 'src/app/utils/request/request';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { Giveaway } from 'src/app/commons/models/giveaway';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';

export interface GiveawayActionsSuccessEvent {
  action: 'delete',
  data?: any,
}

@Component({
  selector: 'app-giveaway-actions',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzPopconfirmModule,
    NzToolTipModule,
    NzIconModule,
    NzDropDownModule,
  ],
  templateUrl: './giveaway-actions.component.html',
  styleUrls: ['./giveaway-actions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GiveawayActionsComponent implements OnInit {

  @Input({ required: true }) giveaway!: Giveaway | null;
  @Input() type: 'buttons' | 'dropdown' = 'buttons';
  @Input() notify = false;
  @Input() request = new Request();

  @Output() onSuccess$: EventEmitter<GiveawayActionsSuccessEvent> = new EventEmitter();

  deleteRequest!: Request;

  private _injector = inject(Injector);

  constructor(
    private _authS: AuthService,
    private _giveawayHttpS: GiveawayHttpService,
    private _routeS: RouteService,
  ) { }

  ngOnInit(): void {
    this.deleteRequest = new Request({
      send: () => this._giveawayHttpS.delete(this.giveaway?.data.id || 0),
      success: (res) => this.onSuccess$.emit({ action: 'delete', data: res.body.giveaway }),
      bind: this.request,
      notifySuccess: this.notify,
      injector: this._injector,
    });
  }

  /* -------------------- */

  canShowActions(): boolean {
    return this.canShowEdit()
      || this.canShowDelete();
  }

  canShowEdit(): boolean {
    return this._routeS.currentPage().name !== 'GiveawaySavePage';
  }

  canShowDelete(): boolean {
    return this._authS.systemUser().can('GiveawayDelete');
  }
}
