<ng-container *ngIf="giveaway">
  <ng-container *ngIf="type === 'buttons'">
    <ng-container *ngIf="canShowEdit()">
      <a class="app-fs-18 mx-1"
        nz-tooltip [nzTooltipTitle]="giveaway.data.status === 'Pending' ? 'Editar' : 'Ver'"
        [routerLink]="['/sorteos', giveaway.data.id]"
      >
        <span class="text-primary" nz-icon [nzType]="giveaway.data.status === 'Pending' ? 'edit' : 'eye'" nzTheme="outline"></span>
      </a>
    </ng-container>

    <ng-container *ngIf="canShowDelete()">
      <button class="btn-styless app-fs-18 mx-1"
        nz-tooltip nzTooltipTitle="Borrar"
        nz-popconfirm
        [nzPopconfirmTitle]="'¿Estás seguro de eliminar el sorteo ' + giveaway.data.name + '?'"
        (nzOnConfirm)="deleteRequest.run()"
      >
        <span class="text-danger" nz-icon nzType="delete" nzTheme="outline"></span>
      </button>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="type === 'dropdown' && canShowActions()">
    <button class="btn-styless app-fs-18 mx-1"
      nz-dropdown
      [nzDropdownMenu]="actionsMenu"
    >
      <span nz-icon nzType="more" nzTheme="outline"></span>
    </button>
    <nz-dropdown-menu #actionsMenu="nzDropdownMenu">
      <ul nz-menu>
        <li nz-menu-item class="text-primary app-fs-16">
          <strong>Acciones</strong>
        </li>
        <li nz-menu-divider></li>
        <li *ngIf="canShowEdit()" nz-menu-item>
          <a class="link-styless" [routerLink]="['/sorteos', giveaway.data.id]">
            Editar
          </a>
        </li>
        <li *ngIf="canShowDelete()" nz-menu-item>
          <button class="btn-styless text-danger"
            nz-popconfirm
            [nzPopconfirmTitle]="'¿Estás seguro de eliminar el sorteo ' + giveaway.data.name + '?'"
            (nzOnConfirm)="deleteRequest.run()"
          >
            Borrar
          </button>
        </li>
      </ul>
    </nz-dropdown-menu>
  </ng-container>
</ng-container>
