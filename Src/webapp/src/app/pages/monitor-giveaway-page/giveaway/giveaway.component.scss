.roulette-container {
  width: 350px;
  height: 80px;
  overflow: hidden;
  border-radius: 20px;
  background: linear-gradient(145deg, #444, #111);
  box-shadow: inset 0 5px 15px rgba(0,0,0,0.6),
              0 8px 20px rgba(0,0,0,0.5);
  margin: 2rem auto;
  position: relative;
  border: 3px solid #eeeeee;
}

.roulette {
  transition: transform 0.08s cubic-bezier(0.33, 1, 0.68, 1); // easing premium
}

.name {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-family: 'Luckiest Guy', cursive;
  color: #fff;
  text-shadow: 0 2px 5px rgba(0,0,0,0.7), 0 0 10px rgba(0,0,0,0.3);
  user-select: none;
  position: relative;
  transition: color 0.3s ease, transform 0.3s ease;
  letter-spacing: 1px;
  background-size: 200% 100%;
  background-position: center;
}

.name.winner {
  font-size: 28px;
  font-weight: 900;
  animation: winnerGlow 1s ease-in-out infinite alternate;
  color: #ffd700;
  text-shadow: 0 0 15px gold, 0 0 25px #ffec7a, 0 0 35px #ffe766;
}

@keyframes winnerGlow {
  from {
    transform: scale(1.05);
    text-shadow: 0 0 10px gold, 0 0 20px #ffec7a;
  }
  to {
    transform: scale(1.15);
    text-shadow: 0 0 20px gold, 0 0 40px #ffec7a, 0 0 60px #ffd700;
  }
}

/* Botón premium */
button.btn-success {
  font-size: 22px;
  padding: 0.8rem 2.5rem;
  font-weight: bold;
  border-radius: 12px;
  background: linear-gradient(145deg, #28a745, #1e7e34);
  color: #fff;
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.4), inset 0 -2px 5px rgba(255,255,255,0.2);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

button.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.5), inset 0 -2px 5px rgba(255,255,255,0.2);
}

button.btn-success:active {
  transform: translateY(1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.4), inset 0 -1px 3px rgba(255,255,255,0.15);
}

/* Fondo premium */
body {
  background: radial-gradient(circle at top, #111, #000);
  color: #fff;
  font-family: 'Arial', sans-serif;
}
