:host {
  height: 100dvh;
  display: block;
  overflow-y: auto;

  app-request {
    display: block;
    height: 100%;

    .content {
      display: grid;
      grid-template-rows: auto 1fr;
      grid-template-areas:
        'header'
        'main'
        ;
      height: 100%;

      > main {
        grid-area: main;
        overflow-y: auto;

        .roulette-container {
          position: relative;
          width: 550px;
          height: 150px;
          overflow: hidden;
          border-radius: 20px;
          background: linear-gradient(145deg, #444, #111);
          box-shadow: inset 0 5px 15px rgba(0,0,0,0.6),
                      0 8px 20px rgba(0,0,0,0.5);
          margin: 4rem auto;
          border: 4px solid var(--bs-primary);

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 10;

            /* efecto 3D */
            filter: drop-shadow(2px 2px 3px rgba(0,0,0,0.5));
            opacity: 0.9;

            /* opcional: un degradado en el borde para simular luz */
            border-color: transparent transparent transparent var(--bs-secondary); // izquierda
            background: linear-gradient(135deg, var(--bs-secondary) 0%, var(--bs-secondary) 100%);
          }

          /* Triángulo izquierdo → apunta a la derecha */
          &::before {
            left: 0;
            border-width: 20px 0 20px 20px;       // top right bottom left
            border-color: transparent transparent transparent var(--bs-secondary); // color en left
          }

          /* Triángulo derecho → apunta a la izquierda */
          &::after {
            right: 0;
            border-width: 20px 20px 20px 0;
            border-color: transparent var(--bs-secondary) transparent transparent;
          }

          &.spinning {
            &::before,
            &::after {
              animation: markerShake 0.2s ease-in-out infinite;
            }
          }

          .roulette {
            transition: transform 0.08s cubic-bezier(0.33, 1, 0.68, 1); // easing premium
          }

          .name {
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-family: 'Luckiest Guy', cursive;
            color: #fff;
            text-shadow: 0 2px 5px rgba(0,0,0,0.7), 0 0 10px rgba(0,0,0,0.3);
            user-select: none;
            position: relative;
            transition: color 0.3s ease, transform 0.3s ease;
            letter-spacing: 1px;
            background-size: 200% 100%;
            background-position: center;

            // degradado suave arriba/abajo
        &::before,
        &::after {
          content: '';
          position: absolute;
          left: 0; right: 0;
          height: 30px;
          pointer-events: none;
        }

        &::before { // fade superior más suave
          top: 0;
          background: linear-gradient(to bottom, rgba(0,0,0,0.2), transparent);
        }

        &::after { // fade inferior más suave
          bottom: 0;
          background: linear-gradient(to top, rgba(0,0,0,0.2), transparent);
        }
          }

          .name.winner {
            font-size: 50px;
            font-weight: 900;
            animation: winnerGlow 1s ease-in-out infinite alternate;
            color: var(--bs-primary);
          }
        }

        button {
          .btn-success {
            font-size: 22px;
            padding: 0.8rem 2.5rem;
            font-weight: bold;
            border-radius: 12px;
            background: linear-gradient(145deg, #28a745, #1e7e34);
            color: #fff;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.4), inset 0 -2px 5px rgba(255,255,255,0.2);
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0,0,0,0.5), inset 0 -2px 5px rgba(255,255,255,0.2);
            }

            &:active {
              transform: translateY(1px);
              box-shadow: 0 4px 12px rgba(0,0,0,0.4), inset 0 -1px 3px rgba(255,255,255,0.15);
            }
          }
        }

        .results {
          display: flex;
          flex-direction: column;
          justify-content: center;
          overflow: hidden;

          &.final {
            width: 100%;

            .results-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(25rem, max-content));
              gap: 1rem;
              align-items: stretch;
              height: 100%;
              overflow-y: auto;
            }

            .result-card {
              height: 100%;
            }
          }

          .result-card {
            display: flex;
            flex-direction: column;
            position: relative;
            color: var(--bs-secondary);
            font-size: var(--app-px-16);
            border-radius: 1rem;
            border: 3px solid var(--bs-primary);
            box-shadow: 0 8px 20px rgba(var(--bs-primary-rgb), 0.3);
            border-color: var(--bs-primary);
            background-color: #fff;
            padding: 1rem;
            width: 25rem;
            min-height: 20rem;
            overflow: hidden;

            .result-header {
              flex: 0 0 auto;

              .prize-img {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                overflow: hidden;
                border: 2px solid var(--bs-primary);
                flex-shrink: 0;
                box-shadow: 0 4px 10px rgba(var(--bs-primary-rgb), 0.3);

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  display: block;
                }
              }
            }

            .winners-list {
              padding: 0.5rem 0;
              overflow-y: auto;

              .loading-dots {
                display: inline-flex;
                span {
                  display: inline-block;
                  margin: 0 2px;
                  font-size: 2em;
                  animation: jump 1s infinite;
                }

                span:nth-child(1) { animation-delay: 0s; }
                span:nth-child(2) { animation-delay: 0.2s; }
                span:nth-child(3) { animation-delay: 0.4s; }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes jump {
  0%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-8px); } /* altura del salto */
}

@keyframes winnerGlow {
  from {
    transform: scale(1.05);
    text-shadow: 0 0 10px var(--bs-secondary), 0 0 20px var(--bs-secondary);
  }
  to {
    transform: scale(1.15);
    text-shadow: 0 0 20px var(--bs-secondary), 0 0 40px var(--bs-secondary), 0 0 60px var(--bs-secondary);
  }
}

@keyframes markerShake {
  0%   { transform: translateY(-50%) translateX(0); }
  25%  { transform: translateY(-50%) translateX(2px); }
  50%  { transform: translateY(-50%) translateX(-2px); }
  75%  { transform: translateY(-50%) translateX(2px); }
  100% { transform: translateY(-50%) translateX(0); }
}