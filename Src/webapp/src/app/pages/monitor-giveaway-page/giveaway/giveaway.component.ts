import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, ElementRef, Injector, signal, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { Giveaway } from 'src/app/commons/models/giveaway';
import { GiveawayPrize } from 'src/app/commons/models/giveaway-prize';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';
import { StateService } from 'src/app/services/state.service';
import { SharedModule } from 'src/app/shared.module';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { Request } from 'src/app/utils/request/request';

declare var require: any;
const confetti = require('canvas-confetti');

@Component({
  selector: 'app-giveaway',
  standalone: true,
  imports: [
    SharedModule,
    RequestComponent,
    NzDividerModule,
  ],
  templateUrl: './giveaway.component.html',
  styleUrls: ['./giveaway.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class GiveawayComponent {

  @ViewChild('participantsList') participantsList!: ElementRef<HTMLDivElement>;

  giveawayId: number = parseInt(this._route.snapshot.paramMap.get('giveawayId') || '');

  giveawayRequest: Request<Giveaway>;
  giveawayStartRequest: Request;
  giveawayRevealWinnerRequest: Request;

  isSpinning = signal(false);

  rouletteNames: string[] = [];
  rouletteTransform = 'translateY(0px)';
  currentWinnerName: string | null = null;
  currentWinner: any | null = null;
  currentPrize = signal<GiveawayPrize | null>(null);

  readonly participantNameHeight = 150; // altura de cada nombre
  stepsPerSecond = 60; // pasos internos por segundo

  spinAudio = new Audio('assets/audios/spinner.mp3');
  winAudio = new Audio('assets/audios/winning.mp3');

  constructor(
    public stateS: StateService,
    private _giveawayHttpS: GiveawayHttpService,
    private _injector: Injector,
    private _route: ActivatedRoute,
    private _cdr: ChangeDetectorRef
  ) {
    this.giveawayRequest = new Request({
      send: () => this._giveawayHttpS.getOne(this.giveawayId, { injector: this._injector }),
      before: () => this.stateS.giveaway()?.clean(),
      success: res => {
        this.stateS.giveaway.set(res.body.giveaway);
        this.currentPrize.set(res.body.giveaway!.getNextPrize());
      }
    });

    this.giveawayStartRequest = new Request({
      send: () => this._giveawayHttpS.start(this.giveawayId, { injector: this._injector }),
      success: res => {
        this.stateS.giveaway.set(res.body.giveaway);
        this.startSpinForPrize(this.stateS.giveaway()!.getNextPrize()?.data.id);
      }
    });

    this.giveawayRevealWinnerRequest = new Request({
      send: () => {
        const input = {
          prize_id: this.currentWinner.prizeId,
          winner_id: this.currentWinner.id,
        };

        return this._giveawayHttpS.revealWinner(this.giveawayId, input, { injector: this._injector });
      },
      success: res => {
        this.stateS.giveaway.set(res.body.giveaway);

        if (this.stateS.giveaway()!.allWinnersRevealed()) {
          setTimeout(() => {
            this.currentPrize.set(null);
          }, 5000);
        }
      }
    });

    /* -------------------- */

    this.giveawayRequest.run();

    /* -------------------- */

    this._injector.get(DestroyRef).onDestroy(() => this.stateS.giveaway.set(null));
  }

  /* -------------------- */

  async startSpinForPrize(prizeId?: number) {
    if (this.isSpinning() || !prizeId) return;

    this.isSpinning.set(true);
    this.currentPrize.set(this.stateS.giveaway()!.getPrizeById(prizeId));

    // Tomar el primer ganador no revelado
    this.currentWinner = this.stateS.giveaway()!.data.winners?.find(winner => winner.prizeId === prizeId && !winner.revealed) || null;

    // Construir lista de nombres
    const { list, winnerIndex } = this.buildRouletteList(this.currentWinner, this.stateS.giveaway()!.data.participants);

    // Animar ruleta
    await this.animateRoulette(list, winnerIndex);

    // Mostrar ganador
    this.currentWinnerName = this.currentWinner.name;
    this._cdr.detectChanges();
    this.playWinAudio();
    this.launchConfetti();

    // Marcar ganador como revelado
    this.giveawayRevealWinnerRequest.run();

    this.isSpinning.set(false);
  }

  getBgGradientForName(name: string): string {
    const hash = Array.from(name).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue1 = hash % 360;
    const hue2 = (hue1 + 120) % 360;
    return `linear-gradient(90deg, hsl(${hue1}, 90%, 60%), hsl(${hue2}, 90%, 50%))`;
  }

  /* -------------------- */

  private buildRouletteList(currentWinner: any, participants: any[]): { list: string[], winnerIndex: number } {
    const names = participants.map(participant => participant.name);
    const result: string[] = [];
    const length = 50 * this.stateS.giveaway()!.data.drawDurationSeconds;

    for (let i = 0; result.length < length; i++) {
      result.push(names[i % names.length]);
    }

    const winnerIndex = result.length - 3;
    result.splice(winnerIndex, 0, currentWinner.name);

    return { list: result, winnerIndex };
  }

  private async animateRoulette(list: string[], winnerIndex: number) {
    this.rouletteNames = list;
    this.rouletteTransform = 'translateY(0px)';
    this.currentWinnerName = null;

    this.playSpinAudio();

    const totalSteps = Math.floor((this.stateS.giveaway()!.data.drawDurationSeconds * 1000) / (1000 / this.stepsPerSecond));

    for (let i = 0; i <= totalSteps; i++) {
      const t = i / totalSteps;
      const easedT = t <= 0.9 ? t : 0.9 + 0.1 * (1 - Math.pow(1 - (t - 0.9) / 0.1, 3));
      const currentPosition = easedT * (winnerIndex * this.participantNameHeight);

      this.rouletteTransform = `translateY(-${currentPosition}px)`;

      // Aplicar blur
      const blurAmount = 5 * (1 - t); // más blur al inicio, 0 al final
      this.participantsList.nativeElement.style.filter = `blur(${blurAmount}px)`;

      this._cdr.detectChanges();
      await new Promise(resolve => setTimeout(resolve, (1000 / this.stepsPerSecond)));
    }

    // Desactivar blur al terminar
    this.participantsList.nativeElement.style.filter = 'none';

    this.stopSpinAudio();
  }

  private playSpinAudio() {
    if (!this.stateS.giveaway()!.data.audioEnabled) return;

    this.spinAudio.currentTime = 0;
    this.spinAudio.loop = true;
    this.spinAudio.play();
  }

  private stopSpinAudio() {
    if (!this.stateS.giveaway()!.data.audioEnabled) return;

    this.spinAudio.pause();
    this.spinAudio.loop = false;
  }

  private playWinAudio() {
    if (!this.stateS.giveaway()!.data.audioEnabled) return;

    this.winAudio.currentTime = 0;
    this.winAudio.play();
  }

  private launchConfetti() {
    confetti.default({
      particleCount: 300,
      spread: 120,
      origin: { y: 0.6 },
      colors: ['#ffd700', '#ff4747', '#28a745', '#00d4ff', '#ff69b4'],
    });
  }
}
