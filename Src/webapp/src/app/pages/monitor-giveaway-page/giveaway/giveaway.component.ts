import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, ElementRef, Injector, signal, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Giveaway } from 'src/app/commons/models/giveaway';
import { GiveawayHttpService } from 'src/app/services/http/giveaway-http.service';
import { StateService } from 'src/app/services/state.service';
import { SharedModule } from 'src/app/shared.module';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { Request } from 'src/app/utils/request/request';
// import * as confetti from 'canvas-confetti';

declare var require: any;
const confetti = require('canvas-confetti');

@Component({
  selector: 'app-giveaway',
  standalone: true,
  imports: [
    SharedModule,
    RequestComponent,
  ],
  templateUrl: './giveaway.component.html',
  styleUrls: ['./giveaway.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class GiveawayComponent {

  giveawayId: number = parseInt(this._route.snapshot.paramMap.get('giveawayId') || '');

  giveawayRequest: Request<Giveaway>;

  constructor(
    public stateS: StateService,
    private _giveawayHttpS: GiveawayHttpService,
    private _injector: Injector,
    private _route: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) {
    this.giveawayRequest = new Request({
      send: () => this._giveawayHttpS.getOne(this.giveawayId, { injector: this._injector }),
      before: () => this.stateS.giveaway()?.clean(),
      success: res => {
        this.stateS.giveaway.set(res.body.giveaway);
      }
    });

    /* -------------------- */

    this.giveawayRequest.run();

    /* -------------------- */

    this._injector.get(DestroyRef).onDestroy(() => this.stateS.giveaway.set(null));
  }

  /* -------------------- */

  @ViewChild('participantsList') participantsList!: ElementRef<HTMLDivElement>;

  participants: string[] = ['Juan', 'Ana', 'Pedro', 'Carla', 'Lucas', 'Sofía', 'Martín', 'Laura', 'Diego'];
  rouletteNames: string[] = [];
  rouletteTransform = 'translateY(0px)';
  currentWinnerName: string | null = null;

  drawing = signal(false);

  readonly ITEM_HEIGHT = 80;         // altura de cada nombre
  spinDuration = 10000;               // duración total del spin en ms
  stepsPerSecond = 60;               // pasos internos por segundo

  spinAudio = new Audio('assets/audios/spinner.mp3');
  winAudio = new Audio('assets/audios/winning.mp3');

  async startSpin() {
    if (this.drawing()) return;
    this.drawing.set(true);

    const winner = this.participants[Math.floor(Math.random() * this.participants.length)];
    const scrollList = this.fillCyclically(this.participants, 50);
    const winnerIndex = scrollList.length - 3;
    scrollList.splice(winnerIndex, 0, winner);

    this.rouletteNames = scrollList;
    this.rouletteTransform = 'translateY(0px)';
    this.currentWinnerName = null;
    this.cdr.detectChanges();
    await this.delay(50);

    // Iniciar audio de spin en loop
    this.spinAudio.currentTime = 0;
    this.spinAudio.loop = true;
    this.spinAudio.play();

    const totalSteps = Math.floor(this.spinDuration / (1000 / this.stepsPerSecond));

    const fps = 60;
    const totalSteps = fps * (this.spinDuration / 1000); // total steps según FPS
    const stepTime = this.spinDuration / totalSteps;      // tiempo real por paso

    for (let i = 0; i <= totalSteps; i++) {
      const t = i / totalSteps;

      // Easing: 90% rápido, 10% desacelera
      let easedT: number;
      if (t <= 0.9) {
        easedT = t;
      } else {
        const x = (t - 0.9) / 0.1;
        easedT = 0.9 + 0.1 * (1 - Math.pow(1 - x, 3));
      }

      const currentPosition = easedT * (winnerIndex * this.ITEM_HEIGHT);
      this.rouletteTransform = `translateY(-${currentPosition}px)`;
      this.cdr.detectChanges();

      await this.delay(stepTime);
    }

    // Final
    this.spinAudio.pause();
    this.spinAudio.loop = false;

    this.currentWinnerName = winner;
    this.cdr.detectChanges();
    this.playWinAudio();
    this.launchConfetti();

    this.drawing.set(false);
  }

  fillCyclically(array: string[], length: number): string[] {
    const result: string[] = [];
    let i = 0;
    while (result.length < length) {
      result.push(array[i % array.length]);
      i++;
    }
    return result;
  }

  playWinAudio() {
    this.winAudio.currentTime = 0;
    this.winAudio.play();
  }

  launchConfetti() {
    confetti.default({
      particleCount: 300,
      spread: 120,
      origin: { y: 0.6 },
      colors: ['#ffd700', '#ff4747', '#28a745', '#00d4ff', '#ff69b4'],
    });
  }

  delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  easeOutCubic(t: number): number {
    return 1 - Math.pow(1 - t, 3);
  }

  getGradientForName(name: string): string {
    const hash = Array.from(name).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const hue1 = hash % 360;
    const hue2 = (hue1 + 120) % 360;
    return `linear-gradient(90deg, hsl(${hue1}, 90%, 60%), hsl(${hue2}, 90%, 50%))`;
  }
}
