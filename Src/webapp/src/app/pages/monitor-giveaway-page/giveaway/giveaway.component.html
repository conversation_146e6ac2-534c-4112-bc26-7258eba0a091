<div class="container text-center h-100">
  <app-request [request]="giveawayRequest" height="100%">
    <div class="content">
      <header>
        <div class="container mb-4 py-3">
          <ng-container *ngIf="stateS.giveaway()?.data?.multimediaConfig?.data?.logo">
            <img [src]="stateS.giveaway()?.data?.multimediaConfig?.data?.logo?.data?.originalUrl" style="width: 14rem;">
          </ng-container>
        </div>
      </header>

      <main>
        <ng-container *ngIf="stateS.giveaway()">
          <div class="py-4 text-center d-flex align-items-baseline justify-content-around h-100">
            <div class="d-flex flex-column align-items-center justify-content-center h-100">
              <h2 class="mb-4">🎉 Sorteo {{ stateS.giveaway()!.data.name }}</h2>

              <ng-container *ngIf="currentPrize(); else allWinnersRevealedTpl">
                <div class="roulette-container" [ngClass]="{ 'spinning': isSpinning() }">
                  <div class="roulette" #participantsList [style.transform]="rouletteTransform">
                    <ng-container *ngFor="let name of rouletteNames">
                      <div class="name" [ngClass]="{ 'winner': name === currentWinnerName }" [ngStyle]="{ 'background-image': getBgGradientForName(name) }">
                        {{ name }}
                      </div>
                    </ng-container>
                  </div>
                </div>

                <div class="mt-4">
                  <ng-container *ngIf="stateS.giveaway()!.data.status === 'Pending'">
                    <button class="btn btn-primary sc-btn-bg-primary sc-btn-text-primary btn-lg" (click)="giveawayStartRequest.run()" [disabled]="isSpinning()">
                      Comenzar
                    </button>
                  </ng-container>

                  <ng-container *ngIf="stateS.giveaway()!.data.status === 'InProgress'">
                    <button class="btn btn-primary sc-btn-bg-primary sc-btn-text-primary btn-lg" (click)="startSpinForPrize(stateS.giveaway()!.getNextPrize()?.data?.id)" [disabled]="isSpinning()">
                      Siguiente
                    </button>
                  </ng-container>
                </div>
              </ng-container>

              <ng-template #allWinnersRevealedTpl>
                <p class="app-fs-28 my-5">¡Todos los ganadores han sido anunciados!</p>

                <div class="results">
                  <h3 class="mb-4 fw-bold">Resultados</h3>

                  <div class="row justify-content-center">
                    <ng-container *ngFor="let prize of stateS.giveaway()!.getPrizesWithWinners()">
                      <div class="col-auto">
                        <ng-container *ngTemplateOutlet="resultCardTpl; context: { prize: prize }"></ng-container>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </ng-template>
            </div>

            <ng-container *ngIf="currentPrize()">
              <div class="results">
                <h3 class="mb-4">Premio</h3>

                <ng-container *ngTemplateOutlet="resultCardTpl; context: { prize: currentPrize() }"></ng-container>
              </div>
            </ng-container>
          </div>
        </ng-container>
      </main>
    </div>
  </app-request>
</div>

<ng-template #resultCardTpl let-prize="prize">
  <div class="result-card h-100">
    <div class="result-header">
      <div class="d-flex align-items-center justify-content-around" style="min-height: 100px;">
        <div class="d-flex flex-column align-items-center justify-content-center">
          <h4 class="app-fs-24 lh-1 mb-0 text-black">
            <strong>{{ prize!.data.name }}</strong>
          </h4>

          <ng-container *ngIf="prize!.data.description">
            <p class="app-fs-16 lh-1 mt-2 mb-0 text-black" style="max-width: 175px;">
              <small>{{ prize!.data.description }}</small>
            </p>
          </ng-container>
        </div>

        <ng-container *ngIf="prize!.data.image">
          <div class="prize-img ms-2">
            <img [src]="prize!.data.image?.data?.originalUrl">
          </div>
        </ng-container>
      </div>

      <nz-divider class="my-3"></nz-divider>

      <p class="app-fs-20 lh-1 mb-2 text-black">Ganadores:</p>
    </div>

    <div class="winners-list">
      <ul class="list-unstyled mb-0">
        <li class="app-fs-24 lh-1 mb-2" *ngFor="let winner of stateS.giveaway()!.getRevealedWinnersForPrize(prize!.data.id); index as i">
          <strong>{{ i + 1 }}. {{ winner.name }}</strong>
        </li>
      </ul>

      <ng-container *ngIf="stateS.giveaway()!.getRevealedWinnersForPrize(prize!.data.id).length < prize!.data.quantity">
        <p class="app-fs-20 lh-1 mb-2 text-black loading-dots">
          <span>.</span><span>.</span><span>.</span>
        </p>
      </ng-container>
    </div>
  </div>
</ng-template>