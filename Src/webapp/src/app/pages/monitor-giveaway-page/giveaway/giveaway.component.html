<div class="container text-center">
  <app-request [request]="giveawayRequest">
    <header>
      <div class="container mb-4 py-3">
        <h1 class="d-flex align-items-center justify-content-center sc-text-primary">
          <ng-container *ngIf="stateS.giveaway()?.data?.logo; else noLogoTpl">
            <img [src]="stateS.giveaway()?.data?.logo?.data?.originalUrl" style="width: 14rem;">
          </ng-container>

          <ng-template #noLogoTpl>
            <!-- <strong class="app-fs-28">{{ stateS.channel()!.data.name }}</strong> -->
          </ng-template>
        </h1>
      </div>
    </header>

    <main>
      <div class="container py-4 text-center">
        <h2 class="mb-4" style="text-shadow: 0 2px 10px gold;">🎉 Sorteo Premium</h2>

        <div class="roulette-container">
          <div class="roulette" [style.transform]="rouletteTransform">
            <div
              class="name"
              *ngFor="let name of rouletteNames"
              [ngClass]="{ 'winner': name === currentWinnerName }"
              [ngStyle]="{ 'background-image': getGradientForName(name) }"
            >
              {{ name }}
            </div>
          </div>
        </div>

        <div class="mt-4">
          <button class="btn btn-success btn-lg" (click)="startSpin()" [disabled]="drawing()">
            Comenzar Sorteo
          </button>
        </div>
      </div>
    </main>
  </app-request>
</div>

