import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AppInteractionComponent } from 'src/app/components/commons/app-interaction/app-interaction.component';
import { SharedModule } from 'src/app/shared.module';
import GiveawayComponent from './giveaway/giveaway.component';

@Component({
  selector: 'app-monitor-giveaway-page',
  standalone: true,
  imports: [
    SharedModule,
    AppInteractionComponent,
    GiveawayComponent,
  ],
  templateUrl: './monitor-giveaway-page.component.html',
  styleUrls: ['./monitor-giveaway-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export default class MonitorGiveawayPageComponent {
  //
}
