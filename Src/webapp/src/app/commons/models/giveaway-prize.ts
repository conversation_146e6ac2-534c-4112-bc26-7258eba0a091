import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Giveaway } from "./giveaway";
import { Media } from "./media";

export interface IGiveawayPrize {
  id: number,
  name: string,
  description: string,
  quantity: number,
  drawDurationSeconds: number,
  drawAllAtOnce: boolean,
  minPosition?: number,
  maxPosition?: number,
  image?: Media,
  createdAt: Moment,
  updatedAt: Moment,
}

export class GiveawayPrize extends BaseModel<IGiveawayPrize, 'backend'> {

  /* -------------------- */

  protected _parsers() {
    return {
      'backend': (data: any): IGiveawayPrize => {
        return {
          ...data,
          image: data.image ? new Media(data.image, { parser: 'backend' }) : undefined,
          createdAt: moment(data.createdAt),
          updatedAt: moment(data.updatedAt),
        };
      }
    }
  }
}
