import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Quiz } from "./quiz";
import { GiveawayPrize } from "./giveaway-prize";
import { IAuth } from "src/app/services/auth.service";
import { Media } from "./media";
import { StyleConfig } from "./style-config";
import { MultimediaConfig } from "./multimedia-config";

export interface IGiveaway {
  id: number,
  name: string,
  monitorPin: string,
  quiz?: Quiz,
  status: 'Pending' | 'InProgress' | 'Completed',
  statusEnum: {
    color: string,
    label: string
  },
  allowedParticipantsMode: 'All' | 'NonWinners' | 'CorrectAnswers' | 'TopRanking',
  allowedParticipantsModeEnum: {
    label: string
  },
  multimediaConfig?: MultimediaConfig,
  styleConfig?: StyleConfig,
  minCorrectAnswers?: number,
  audioEnabled: boolean,
  drawDurationSeconds: number,
  participants: any[],
  winners?: any[],
  winnersCount?: number,
  revealedWinnersCount?: number,
  blackList?: any[],
  logo?: Media,
  bgVideo?: Media,
  monitorBgVideo?: Media,
  monitorUrl?: string,
  messagePrimary?: string,
  messageSecondary?: string,
  prizes: GiveawayPrize[],
  createdAt: Moment,
  updatedAt: Moment,
}

export class Giveaway extends BaseModel<IGiveaway, 'backend'> {

  /* -------------------- */

  protected _parsers() {
    return {
      'backend': (data: any): IGiveaway => {
        return {
          ...data,
          quiz: data.quiz ? new Quiz(data.quiz, { parser: 'backend', injector: this._injector, init: false }) : null,
          prizes: data.prizes?.length ? data.prizes.map((item: any) => new GiveawayPrize(item, { parser: 'backend' })) : [],
          logo: data.logo ? new Media(data.logo, { parser: 'backend' }) : undefined,
          bgVideo: data.bgVideo ? new Media(data.bgVideo, { parser: 'backend' }) : undefined,
          monitorBgVideo: data.monitorBgVideo ? new Media(data.monitorBgVideo, { parser: 'backend' }) : undefined,
          styleConfig: data.styleConfig ? new StyleConfig(data.styleConfig, { parser: 'backend', injector: this._injector }) : undefined,
          multimediaConfig: data.multimediaConfig ? new MultimediaConfig(data.multimediaConfig, { parser: 'backend', injector: this._injector }) : undefined,
          createdAt: moment(data.createdAt),
          updatedAt: moment(data.updatedAt),
        };
      }
    }
  }

  getAuthData(): IAuth['data'] {
    return {
      id: this.data.id,
      name: this.data.name,
      model: this,
    };
  }

  /* -------------------- */

  getNextPrize(): GiveawayPrize | null {
    if (!this.data.winners?.length) return null;

    const nextWinner = this.data.winners.find(winner => !winner.revealed);

    if (!nextWinner) return null;

    const nextPrize = this.data.prizes.find(prize => prize.data.id === nextWinner.prizeId) || null;

    return nextPrize;
  }

  getPrizeById(prizeId: number): GiveawayPrize | null {
    return this.data.prizes.find(prize => prize.data.id === prizeId) || null;
  }

  allWinnersRevealed(): boolean {
    return this.data.winners?.every(winner => winner.revealed) || false;
  }

  getRevealedWinnersForPrize(prizeId: number): any[] {
    return this.data.winners?.filter(winner => winner.prizeId === prizeId && winner.revealed) || [];
  }

  getWinnersForPrize(prizeId: number): any[] {
    return this.data.winners?.filter(winner => winner.prizeId === prizeId) || [];
  }

  getPrizesWithWinners(): GiveawayPrize[] {
    return this.data.prizes.filter(prize => this.getWinnersForPrize(prize.data.id)?.length);
  }
}
