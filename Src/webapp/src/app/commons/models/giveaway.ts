import { Moment } from "moment";
import { BaseModel } from "./base-model";
import * as moment from "moment";
import { Quiz } from "./quiz";
import { GiveawayPrize } from "./giveaway-prize";
import { IAuth } from "src/app/services/auth.service";
import { Media } from "./media";

export interface IGiveaway {
  id: number,
  name: string,
  monitorPin: string,
  quiz?: Quiz,
  status: 'Pending' | 'InProgress' | 'Completed',
  statusEnum: {
    color: string,
    label: string
  },
  allowedParticipantsMode: 'All' | 'NonWinners' | 'CorrectAnswers',
  allowedParticipantsModeEnum: {
    label: string
  },
  minCorrectAnswers?: number,
  colorScheme?: string,
  audioEnabled: boolean,
  drawDurationSeconds: number,
  participants: any[],
  winners?: any[],
  winnersCount?: number,
  blackList?: any[],
  logo?: Media,
  bgVideo?: Media,
  monitorBgVideo?: Media,
  monitorUrl?: string,
  prizes: GiveawayPrize[],
  createdAt: Moment,
  updatedAt: Moment,
}

export class Giveaway extends BaseModel<IGiveaway, 'backend'> {

  /* -------------------- */

  protected _parsers() {
    return {
      'backend': (data: any): IGiveaway => {
        return {
          ...data,
          quiz: data.quiz ? new Quiz(data.quiz, { parser: 'backend' }) : null,
          prizes: data.prizes?.length ? data.prizes.map((item: any) => new GiveawayPrize(item, { parser: 'backend' })) : [],
          logo: data.logo ? new Media(data.logo, { parser: 'backend' }) : undefined,
          bgVideo: data.bgVideo ? new Media(data.bgVideo, { parser: 'backend' }) : undefined,
          monitorBgVideo: data.monitorBgVideo ? new Media(data.monitorBgVideo, { parser: 'backend' }) : undefined,
          createdAt: moment(data.createdAt),
          updatedAt: moment(data.updatedAt),
        };
      }
    }
  }

  getAuthData(): IAuth['data'] {
    return {
      id: this.data.id,
      name: this.data.name,
      model: this,
    };
  }
}
