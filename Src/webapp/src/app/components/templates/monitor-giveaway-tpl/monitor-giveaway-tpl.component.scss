:host {
  display: block;
  width: 100dvw;
  max-width: 420px;
  margin: auto;

  > div {
    position: relative;
    display: grid;
    height: 100dvh;
    width: inherit;
    max-width: inherit;
    grid-template-rows: 1fr auto;
    grid-template-areas:
      // 'header'
      'body'
      'footer'
      ;

    // > header {
    //   grid-area: header;
    //   width: inherit;
    //   max-width: inherit;
    // }

    > main {
      grid-area: body;
      width: inherit;
      max-width: inherit;
    }

    &.centered-body {
      grid-template-rows: auto auto auto;
      grid-template-areas:
        'header'
        'body'
        'footer'
        ;

      > footer {
        grid-area: footer;
        width: inherit;
        max-width: inherit;
      }
    }
  }
}
