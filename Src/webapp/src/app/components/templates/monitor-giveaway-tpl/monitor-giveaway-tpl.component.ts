import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { StateService } from 'src/app/services/state.service';
import { RouterModule } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-monitor-giveaway-tpl',
  standalone: true,
  imports: [
    SharedModule,
    RouterModule,
    NzIconModule,
  ],
  templateUrl: './monitor-giveaway-tpl.component.html',
  styleUrls: ['./monitor-giveaway-tpl.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MonitorGiveawayTplComponent {

  constructor(
    public stateS: StateService
  ) { }
}
