import { ChangeDetectionStrategy, Component, ContentChild, signal, TemplateRef } from '@angular/core';
import { SvgTriviasFunPurpleLogoComponent } from 'src/app/components/svg/svg.components';
import { SharedModule } from 'src/app/shared.module';
import { AppInteractionService } from './app-interaction.service';
import { RequestComponent } from 'src/app/utils/request/components/request/request.component';
import { FormModule } from 'src/app/utils/form/form.module';
import { Form } from 'src/app/utils/form/form';
import { FormBuilder, Validators } from '@angular/forms';
import { ChannelQuizHttpService } from 'src/app/services/http/channel-quiz-http.service';
import { AuthService } from 'src/app/services/auth.service';
import { StateService } from 'src/app/services/state.service';
import { ActivatedRoute } from '@angular/router';
import { MonitorHttpService } from 'src/app/services/http/monitor-http.service';

@Component({
  selector: 'app-interaction',
  standalone: true,
  imports: [
    SharedModule,
    SvgTriviasFunPurpleLogoComponent,
    RequestComponent,
    FormModule
  ],
  templateUrl: './app-interaction.component.html',
  styleUrls: ['./app-interaction.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppInteractionComponent {

  @ContentChild('content') contentTpl!: TemplateRef<any>;

  quizId: number = parseInt(this._route.snapshot.paramMap.get('quizId') || '');
  giveawayId: number = parseInt(this._route.snapshot.paramMap.get('giveawayId') || '');

  showLoginWithPin = signal(this.giveawayId ? true : false);

  form: Form;

  constructor(
    public appInteractionS: AppInteractionService,
    public authS: AuthService,
    public stateS: StateService,
    private _fb: FormBuilder,
    private _channelQuizHttpS: ChannelQuizHttpService,
    private _monitorHttpS: MonitorHttpService,
    private _route: ActivatedRoute,
  ) {
    this.form = new Form(this._fb.group({
      monitor_pin: ['S91823', [Validators.required]],
      type: [this.giveawayId ? 'giveaway' : 'quiz'],
    }), {
      request: {
        send: () => this._monitorHttpS.login({ id: this.giveawayId || this.quizId, ...this.form.group.value }),
        success: () => {
          this.appInteractionS.force.set(false);
        },
        reset: true,
      },
    });
  }

}
