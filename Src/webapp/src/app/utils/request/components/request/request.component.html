<ng-template #projectedContentTpl>
  <ng-content />
</ng-template>

<!-- -------------------- -->

<ng-container *ngIf="remainingRequests; else oneRequestLeftTpl">
  <app-request [request]="firstRequest" [layer]="layer" [minHeight]="minHeight">
    <app-request [requests]="requests" [layer]="layer" [minHeight]="minHeight">
      <ng-container [ngTemplateOutlet]="projectedContentTpl" />
    </app-request>
  </app-request>
</ng-container>

<ng-template #oneRequestLeftTpl>
  <div [style.minHeight]="minHeight" [style.height]="height">
    <ng-container [ngTemplateOutlet]="projectedContentTpl" />

    <ng-container *ngIf="type === 'default'" [ngTemplateOutlet]="typeDefaultTpl" />
    <ng-container *ngIf="type === 'spinner'" [ngTemplateOutlet]="typeSpinnerTpl" />
    <ng-container *ngIf="type === 'form'" [ngTemplateOutlet]="typeFormTpl" />

    <!-- -------------------- -->

    <ng-template #typeDefaultTpl>
      <ng-container *ngIf="request.body(); else noResTpl">
        <ng-container *ngTemplateOutlet="bodyTpl" />
      </ng-container>

      <ng-template #noResTpl>
        <ng-container [ngTemplateOutlet]="placeholderTpl" />
      </ng-template>

      <div *ngIf="mustDisableInteraction" class="interaction-disabled" [class.show-layer]="layer">
        <div>
          <ng-container *ngIf="isLoading" [ngTemplateOutlet]="loadingTpl || defaultLoadingTpl" />
          <ng-container *ngIf="request.isError()" [ngTemplateOutlet]="errorTpl || defaultErrorTpl" />
        </div>
      </div>
    </ng-template>

    <ng-template #typeSpinnerTpl>
      <div *ngIf="request.isLoading()" class="interaction-disabled" [class.show-layer]="layer">
        <div>
          <ng-container *ngIf="isLoading" [ngTemplateOutlet]="loadingTpl || defaultLoadingTpl" />
        </div>
      </div>
    </ng-template>

    <ng-template #typeFormTpl>
      <div *ngIf="request.isLoading()" class="interaction-disabled"></div>
    </ng-template>

    <!-- -------------------- -->

    <ng-template #defaultErrorTpl>
      <div class="text-center p-3">
        <ng-container [ngSwitch]="request.err()?.name">
          <ng-container *ngSwitchCase="'NO_QUIZ_ERROR'">
            <p class="lead mb-0 text-danger"><strong>No se encontró la partida</strong></p>
          </ng-container>

          <ng-container *ngSwitchCase="'INACTIVE_QUIZ_ERROR'">
            <p class="lead mb-0 text-danger"><strong>La partida no está activa</strong></p>
          </ng-container>

          <ng-container *ngSwitchCase="'INACTIVE_CHANNEL_ERROR'">
            <p class="lead mb-0 text-danger"><strong>El canal no está activo</strong></p>
          </ng-container>

          <ng-container *ngSwitchDefault>
            <p class="lead mb-0 text-danger"><strong>Ha ocurrido un error</strong></p>
            <p class="mb-0 text-dark">No podemos mostrar esta información por el momento</p>
          </ng-container>
        </ng-container>
      </div>
    </ng-template>

    <ng-template #defaultLoadingTpl>
      <div class="spinner-border text-primary sc-text-secondary" role="status"
        [ngStyle]="{
          'width': spinnerSize + 'rem',
          'height': spinnerSize + 'rem',
        }"
      >
        <span class="visually-hidden">Loading...</span>
      </div>
    </ng-template>
  </div>
</ng-template>
