import { Injectable, Injector } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { GUARD, MAP } from 'src/app/interceptors/contexts';
import { parseQueryParams } from 'src/app/helper';
import { environment } from 'src/environments/environment';
import { Giveaway } from 'src/app/commons/models/giveaway';

@Injectable({
  providedIn: 'root'
})
export class GiveawayHttpService {

  constructor(
    private _httpClient: HttpClient,
  ) { }

  /* -------------------- */

  getOne = (giveawayId: number, options?: { params?: any, injector?: Injector }) => {
    const queryParams = parseQueryParams(options?.params);

    return this._httpClient.get(`${environment.backendUrl}/api/v1/webapp/giveaways/${giveawayId}?${queryParams}`, {
      context: new HttpContext()
        .set(GUARD, 'giveaway')
        .set(MAP, res => {
          res.body.giveaway = new Giveaway(res.body.giveaway, { parser: 'backend', injector: options?.injector });
          return res.body;
        })
    });
  }
}
