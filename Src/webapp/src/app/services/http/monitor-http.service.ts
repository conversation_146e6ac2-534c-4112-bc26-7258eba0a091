import { Injectable, Injector } from '@angular/core';
import { HttpClient, HttpContext } from '@angular/common/http';
import { GUARD, MAP, ON_SUCCESS } from 'src/app/interceptors/contexts';
import { parseQueryParams } from 'src/app/helper';
import { environment } from 'src/environments/environment';
import { Quiz } from 'src/app/commons/models/quiz';
import { AuthService } from '../auth.service';

@Injectable({
  providedIn: 'root'
})
export class MonitorHttpService {

  constructor(
    private _httpClient: HttpClient,
    private _authS: AuthService,
  ) { }

  /* -------------------- */

  login = (input: { id: number, monitor_pin: string, type: string }) => {
    return this._httpClient.post(`${environment.backendUrl}/auth/v1/monitor/login`, input, {
      context: new HttpContext()
        .set(GUARD, 'appClient')
        .set(ON_SUCCESS, res => {
          const token = res.body.token;
          const tokenExpiresAt = res.body.token_expires_at;

          if (input.type === 'quiz') {
            this._authS.quiz().login({
              data: {
                id: res.body.data.id,
                name: res.body.data.name,
              },
              token,
              tokenExpiresAt,
            });
          } else {
            this._authS.giveaway().login({
              data: {
                id: res.body.data.id,
                name: res.body.data.name,
              },
              token,
              tokenExpiresAt,
            });
          }
        })
    });
  }
}
