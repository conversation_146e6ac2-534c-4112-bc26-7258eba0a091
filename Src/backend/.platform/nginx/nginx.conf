#Elastic Beanstalk Nginx Configuration File

user nginx;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;
worker_processes auto;
worker_rlimit_nofile 200000;

events {
  worker_connections 32768;
}

http {
  server_tokens off;

  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                  '$status $body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for"';

  include conf.d/*.conf;

  map $http_upgrade $connection_upgrade {
    default "upgrade";
  }

  server {
    listen 80 default_server;
    access_log /var/log/nginx/access.log main;

    client_header_timeout 60;
    client_body_timeout 60;
    keepalive_timeout 60;

    # gzip on;
    # gzip_comp_level 4;
    # gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 256;
    gzip_types
      application/octet-stream
      application/atom+xml
      application/geo+json
      application/javascript
      application/x-javascript
      application/json
      application/ld+json
      application/manifest+json
      application/rdf+xml
      application/rss+xml
      application/xhtml+xml
      application/xml
      font/eot
      font/otf
      font/ttf
      font/woff
      font/woff2
      image/svg+xml
      text/css
      text/javascript
      text/plain
      text/xml;

    # Include the Elastic Beanstalk generated locations
    include conf.d/elasticbeanstalk/*.conf;
  }
}
