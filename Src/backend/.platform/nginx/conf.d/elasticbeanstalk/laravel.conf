index index.php;

charset utf-8;

add_header X-Frame-Options "SAMEORIGIN";
add_header X-Content-Type-Options "nosniff";

client_max_body_size 10m;

location = /favicon.ico { access_log off; log_not_found off; }
location = /robots.txt  { access_log off; log_not_found off; }

location ~* .(?:css|js|ttf|woff|woff2|svg|png|jpg|gif|\.ico)$ {
  expires 1y;
  add_header Cache-Control "public";
}

location ~ /\.(?!well-known).* {
  deny all;
}

location / {
  try_files $uri $uri/ /index.php?$query_string;
}
