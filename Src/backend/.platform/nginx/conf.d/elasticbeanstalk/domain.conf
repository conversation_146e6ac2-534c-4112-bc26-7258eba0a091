set $must_redirect '';

if ($http_user_agent ~* '^ELB-HealthChecker\/.*$') {
  set $must_redirect '${must_redirect}0';
}

if ($host !~* '^(backend|backend-time).trivias.fun$') {
  set $must_redirect '${must_redirect}1';
}

if ($http_x_forwarded_proto = 'http') {
  set $must_redirect '${must_redirect}1';
}

if ($must_redirect ~ '^(1+)$') {
  return 301 https://backend.trivias.fun$request_uri;
}

location /health-check {
  add_header Content-Type "text/plain";
  return 200;
}
