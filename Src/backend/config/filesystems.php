<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => storage_path('app/public'),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],

        'media' => [
            'driver' => 'local',
            'root' => storage_path('app/public/media'),
            'url' => env('APP_URL').'/storage/media',
            'visibility' => 'public',
            'throw' => false,
        ],

        'media_tmp' => [
            'driver' => 'local',
            'root' => storage_path('app/public/media-tmp'),
            'url' => env('APP_URL').'/storage/media-tmp',
            'visibility' => 'public',
            'throw' => false,
        ],

        'rankings' => [
            'driver' => 'local',
            'root' => storage_path('app/public/rankings'),
            'url' => env('APP_URL').'/storage/rankings',
            'visibility' => 'public',
            'throw' => false,
        ],

        's3_media' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('S3_MEDIA_BUCKET'),
            'url' => env('S3_MEDIA_URL'),
            'endpoint' => env('S3_MEDIA_ENDPOINT'),
            'use_path_style_endpoint' => env('S3_MEDIA_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

        's3_media_tmp' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('S3_MEDIA_TMP_BUCKET'),
            'url' => env('S3_MEDIA_TMP_URL'),
            'endpoint' => env('S3_MEDIA_TMP_ENDPOINT'),
            'use_path_style_endpoint' => env('S3_MEDIA_TMP_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

        's3_rankings' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('S3_RANKINGS_BUCKET'),
            'url' => env('S3_RANKINGS_URL'),
            'endpoint' => env('S3_RANKINGS_ENDPOINT'),
            'use_path_style_endpoint' => env('S3_RANKINGS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('S3_BUCKET'),
            'url' => env('S3_URL'),
            'endpoint' => env('S3_ENDPOINT'),
            'use_path_style_endpoint' => env('S3_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
