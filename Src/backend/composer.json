{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "baopham/dynamodb": "^6.4", "beyondcode/laravel-websockets": "^1.14", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.15", "laravel/sanctum": "^3.2", "laravel/telescope": "^4.17", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1", "marvinlabs/laravel-discord-logger": "^1.4", "mvanduijker/laravel-model-exists-rule": "^3.1", "predis/predis": "^2.2", "pusher/pusher-php-server": "^7.2", "spatie/laravel-data": "^4.2", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.9"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}