<?php

use App\Commons\Response\Response;
use App\Http\Controllers\Api\v1\Webapp\ChannelQuizController;
use App\Http\Controllers\Api\v1\Webapp\GiveawayController;
use Illuminate\Support\Facades\Route;



/* -------------------- */

Route::middleware(['auth:app_client'])->group(function () {
    Route::get('/', function () { return Response::jsonHello(); });
    /* -------------------- */
    Route::get('/get-server-time', function () {
        date_default_timezone_set(config('app.timezone'));

        header('Access-Control-Allow-Origin: *');
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('X-Accel-Buffering: no');

        while (true) {
            echo 'data: ' . now()->toISOString(), "\n\n";

            while (ob_get_level() > 0) {
                ob_end_flush();
            }

            flush();

            if (connection_aborted()) {
                break;
            }

            usleep(100 * 1000);
        }
    });
    /* -------------------- */
    Route::get('/channels/{channelSlug}', 'ChannelController@getBySlug');
});


Route::middleware(['auth:quiz'])->group(function () {
    Route::controller(ChannelQuizController::class)->group(function () {
        Route::post('/channels/{channelId}/quizzes/{quizId}/play', 'play');
        Route::post('/channels/{channelId}/quizzes/{quizId}/pause', 'pause');
    });
});

Route::middleware(['auth:giveaway'])->group(function () {
    Route::controller(GiveawayController::class)->group(function () {
        Route::get('/giveaways/{giveawayId}', 'GiveawayController@show');
        Route::post('/giveaways/{giveawayId}/start', 'GiveawayController@start');
        Route::post('/giveaways/{giveawayId}/reveal-winner', 'GiveawayController@revealWinner');
    });
});

Route::middleware(['auth:user', 'verified'])->group(function () {
    Route::get('/channels/{channelId}/quizzes', 'ChannelQuizController@index');
    Route::get('/channels/{channelId}/quizzes/{quizId}', 'ChannelQuizController@show');
    /* -------------------- */
    Route::post('/channels/{channelId}/quizzes/{quizId}/user/access', 'ChannelQuizUserController@access');
    Route::post('/channels/{channelId}/quizzes/{quizId}/user/register-profile', 'ChannelQuizUserController@registerProfile');
    Route::patch('/channels/{channelId}/quizzes/{quizId}/user/update-current-step-id-profile', 'ChannelQuizUserController@updateCurrentStepIdProfile');
    Route::post('/channels/{channelId}/quizzes/{quizId}/user/set-progress', 'ChannelQuizUserController@setProgress');
    Route::post('/channels/{channelId}/quizzes/{quizId}/user/set-end-email', 'ChannelQuizUserController@setEndEmail');
});
