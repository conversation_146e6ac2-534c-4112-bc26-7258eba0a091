<?php

use App\Commons\Response\Response;
use App\Enums\PermissionEnum;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:app_client'])->group(function () {
    Route::get('/', function () { return Response::json<PERSON>ello(); });
    /* -------------------- */
});

Route::middleware(['auth:app_client,system_user'])->group(function () {
    Route::get('/combos', 'CombosController@get');
    Route::get('/validate', 'ValidateController@validate');
});

Route::middleware(['auth:system_user', 'verified'])->group(function () {
    Route::post('/upload', 'UploadController@upload');
    /* -------------------- */
    Route::get('/system-users', 'SystemUserController@index')->can(PermissionEnum::SystemUserGet->name);
    Route::get('/system-users/{systemUserId}', 'SystemUserController@show')->can(PermissionEnum::SystemUserGet->name);
    Route::post('/system-users', 'SystemUserController@create')->can(PermissionEnum::SystemUserCreate->name);
    Route::put('/system-users/{systemUserId}', 'SystemUserController@update')->can(PermissionEnum::SystemUserUpdate->name);
    Route::delete('/system-users/{systemUserId}', 'SystemUserController@delete')->can(PermissionEnum::SystemUserDelete->name);
    Route::patch('/system-users/{systemUserId}/activate', 'SystemUserController@activate')->can(PermissionEnum::SystemUserActivate->name);
    Route::patch('/system-users/{systemUserId}/deactivate', 'SystemUserController@deactivate')->can(PermissionEnum::SystemUserDeactivate->name);
    /* -------------------- */
    Route::get('/channels', 'ChannelController@index')->can(PermissionEnum::ChannelGet->name);
    Route::get('/channels/{channelId}', 'ChannelController@show')->can(PermissionEnum::ChannelGet->name);
    Route::post('/channels', 'ChannelController@create')->can(PermissionEnum::ChannelCreate->name);
    Route::put('/channels/{channelId}', 'ChannelController@update')->can(PermissionEnum::ChannelUpdate->name);
    Route::delete('/channels/{channelId}', 'ChannelController@delete')->can(PermissionEnum::ChannelDelete->name);
    Route::patch('/channels/{channelId}/activate', 'ChannelController@activate')->can(PermissionEnum::ChannelActivate->name);
    Route::patch('/channels/{channelId}/deactivate', 'ChannelController@deactivate')->can(PermissionEnum::ChannelDeactivate->name);
    /* -------------------- */
    Route::get('/questions-sets', 'QuestionSetController@index')->can(PermissionEnum::QuestionSetGet->name);
    Route::get('/questions-sets/{questionSetId}', 'QuestionSetController@show')->can(PermissionEnum::QuestionSetGet->name);
    Route::post('/questions-sets', 'QuestionSetController@create')->can(PermissionEnum::QuestionSetCreate->name);
    Route::put('/questions-sets/{questionSetId}', 'QuestionSetController@update')->can(PermissionEnum::QuestionSetUpdate->name);
    Route::delete('/questions-sets/{questionSetId}', 'QuestionSetController@delete')->can(PermissionEnum::QuestionSetDelete->name);
    Route::patch('/questions-sets/{questionSetId}/activate', 'QuestionSetController@activate')->can(PermissionEnum::QuestionSetActivate->name);
    Route::patch('/questions-sets/{questionSetId}/deactivate', 'QuestionSetController@deactivate')->can(PermissionEnum::QuestionSetDeactivate->name);
    Route::patch('/questions-sets/{questionSetId}/duplicate', 'QuestionSetController@duplicate')->can(PermissionEnum::QuestionSetCreate->name);
    /* -------------------- */
    Route::get('/quizzes', 'QuizController@index')->can(PermissionEnum::QuizGet->name);
    Route::get('/quizzes/{quizId}', 'QuizController@show')->can(PermissionEnum::QuizGet->name);
    Route::post('/quizzes', 'QuizController@create')->can(PermissionEnum::QuizCreate->name);
    Route::put('/quizzes/{quizId}', 'QuizController@update')->can(PermissionEnum::QuizUpdate->name);
    Route::delete('/quizzes/{quizId}', 'QuizController@delete')->can(PermissionEnum::QuizDelete->name);
    Route::patch('/quizzes/{quizId}/activate', 'QuizController@activate')->can(PermissionEnum::QuizActivate->name);
    Route::patch('/quizzes/{quizId}/deactivate', 'QuizController@deactivate')->can(PermissionEnum::QuizDeactivate->name);
    Route::post('/quizzes/{quizId}/duplicate', 'QuizController@duplicate')->can(PermissionEnum::QuizCreate->name);
    Route::post('/quizzes/{quizId}/play', 'QuizController@play')->can(PermissionEnum::QuizPlay->name);
    Route::post('/quizzes/{quizId}/pause', 'QuizController@pause')->can(PermissionEnum::QuizPause->name);
    /* -------------------- */
    Route::get('/user-quiz-profiles', 'UserQuizProfileController@index')->can(PermissionEnum::UserQuizProfileGet->name);
    Route::get('/user-quiz-profiles/{userQuizProfileId}', 'UserQuizProfileController@show')->can(PermissionEnum::UserQuizProfileGet->name);
    Route::delete('/user-quiz-profiles/{userQuizProfileId}', 'UserQuizProfileController@delete')->can(PermissionEnum::UserQuizProfileDelete->name);
    /* -------------------- */
    Route::get('/reports/quizzes/{quizIds}', 'ReportQuizController@get')->can(PermissionEnum::ReportQuizGet->name);
    /* -------------------- */
    Route::get('/giveaways', 'GiveawayController@index');
    Route::get('/giveaways/{giveawayId}', 'GiveawayController@show');
    Route::post('/giveaways', 'GiveawayController@create');
    Route::put('/giveaways/{giveawayId}', 'GiveawayController@update');
    Route::delete('/giveaways/{giveawayId}', 'GiveawayController@delete');
});
