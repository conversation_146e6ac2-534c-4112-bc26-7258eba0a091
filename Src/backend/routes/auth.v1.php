<?php

use App\Commons\Auth\Auth;
use App\Commons\Response\Response;
use Carbon\Carbon;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;

Route::get('/', function () { return Response::json<PERSON><PERSON>(); });
/* -------------------- */
Route::post('/app-client/login', 'AuthAppClientController@login');

Route::middleware(['auth:app_client'])->group(function () {
    Route::post('/app-client/logout', 'AuthAppClientController@logout');
    Route::get('/app-client/me', 'AuthAppClientController@me');
    /* -------------------- */
    Route::post('/system-user/register', 'AuthSystemUserController@register')->middleware(['throttle:3,1']);
    Route::post('/system-user/login', 'AuthSystemUserController@login');
    Route::post('/system-user/password-reset/request', 'AuthSystemUserPasswordResetController@request')->middleware(['throttle:3,1']);
    Route::patch('/system-user/password-reset/update', 'AuthSystemUserPasswordResetController@update');
    /* -------------------- */
    Route::post('/user/fake-register', 'AuthUserController@fakeRegister')->middleware(['throttle:120,1']);
    /* -------------------- */
    Route::post('/quiz/login', 'AuthQuizController@login');
    Route::post('/monitor/login', 'AuthMonitorController@login');
});

Route::middleware(['auth:system_user'])->group(function () {
    Route::post('/system-user/logout', 'AuthSystemUserController@logout');
    Route::get('/system-user/me', 'AuthSystemUserController@me');
    Route::put('/system-user/update', 'AuthSystemUserController@update');
    Route::post('/system-user/verification/request', 'AuthSystemUserVerificationController@request')->middleware(['throttle:3,1']);
    Route::patch('/system-user/verification/verify', 'AuthSystemUserVerificationController@verify');
});

Route::middleware(['auth:user'])->group(function () {
    Route::post('/user/logout', 'AuthUserController@logout');
    Route::get('/user/me', 'AuthUserController@me');
});
