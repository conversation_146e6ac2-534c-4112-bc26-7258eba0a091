<?php

namespace Database\Seeders;

use App\Enums\PermissionEnum;
use App\Enums\RoleEnum;
use App\Facades\Auth;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        foreach ([Auth::getSystemUserGuardName()] as $guardKey => $guard) {
            collect(PermissionEnum::names())->map(function ($item, $key) use ($guardKey, $guard) {
                return [
                    'id' => $key + 1 + ($guardKey * 1000),
                    'name' => $item,
                    'guard_name' => $guard,
                ];
            })->each(function ($item) {
                DB::table(config('permission.table_names.permissions'))->updateOrInsert(['id' => $item['id']], $item);
            });
        }

        foreach ([Auth::getSystemUserGuardName()] as $guardKey => $guard) {
            collect(RoleEnum::names())->map(function ($item, $key) use ($guardKey, $guard) {
                return [
                    'id' => $key + 1 + ($guardKey * 100),
                    'name' => $item,
                    'guard_name' => Auth::getSystemUserGuardName(),
                ];
            })->each(function ($item) {
                DB::table(config('permission.table_names.roles'))->updateOrInsert(['id' => $item['id']], $item);
            });
        }

        /* -------------------- */

        Role::findByName(RoleEnum::Root->name, Auth::getSystemUserGuardName())
            ->syncPermissions(Permission::whereGuardName(Auth::getSystemUserGuardName())->get());

        Role::findByName(RoleEnum::Admin->name, Auth::getSystemUserGuardName())
            ->syncPermissions(Permission::whereGuardName(Auth::getSystemUserGuardName())->get());

        Role::findByName(RoleEnum::Client->name, Auth::getSystemUserGuardName())
            ->syncPermissions(Permission::whereGuardName(Auth::getSystemUserGuardName())->get());

        Role::findByName(RoleEnum::ClientChild->name, Auth::getSystemUserGuardName())
            // ->syncPermissions(Permission::whereGuardName(Auth::getSystemUserGuardName())->get());
            ->syncPermissions([
                //
            ]);
    }
}
