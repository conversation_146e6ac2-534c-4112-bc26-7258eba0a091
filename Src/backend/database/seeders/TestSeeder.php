<?php

namespace Database\Seeders;

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use App\Enums\QuestionTypeEnum;
use App\Enums\QuizModeEnum;
use App\Enums\QuizTypeEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Enums\WhitelistStatusEnum;
use App\Models\Quiz;
use App\Repositories\ChannelRepository;
use App\Repositories\QuestionSetRepository;
use App\Repositories\QuizRepository;
use Illuminate\Database\Seeder;

class TestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!app()->environment('local')) {
            return;
        }

        /* -------------------- */

        $questionSetInput = [
            'id' => 1,
            'name' => 'Set Uno',
            'questions' => [
                [
                    'body' => '¿Cuál es la respuesta de la primera pregunta?',
                    'is_public' => false,
                    'type' => QuestionTypeEnum::Normal->name,
                    'pivot' => [
                        'order' => 1,
                    ],
                    'image' => null,
                    'image_url' => null,
                    'audio' => null,
                    'answers' => [
                        [
                            'body' => 'Respuesta correcta',
                            'is_correct' => true,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ]
                    ]
                ],
                [
                    'body' => '¿Cuál es la respuesta de la segunda pregunta?',
                    'is_public' => false,
                    'type' => QuestionTypeEnum::Double->name,
                    'pivot' => [
                        'order' => 2,
                    ],
                    'image' => null,
                    'image_url' => null,
                    'audio' => null,
                    'answers' => [
                        [
                            'body' => 'Respuesta correcta',
                            'is_correct' => true,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ]
                    ]
                ],
                [
                    'body' => '¿Cuál es la respuesta de la tercera pregunta?',
                    'is_public' => false,
                    'type' => QuestionTypeEnum::Bomb->name,
                    'pivot' => [
                        'order' => 3,
                    ],
                    'image' => null,
                    'image_url' => null,
                    'audio' => null,
                    'answers' => [
                        [
                            'body' => 'Respuesta correcta',
                            'is_correct' => true,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ],
                        [
                            'body' => 'Respuesta incorrecta',
                            'is_correct' => false,
                        ]
                    ]
                ]
            ]
        ];

        /* -------------------- */

        $quizInput = [
            'id' => 1,
            'channel' => [
                'id' => 1
            ],
            'question_set' => [
                'id' => 1
            ],
            'name' => 'Primera Partida',
            'type' => QuizTypeEnum::Live->name,
            'mode' => QuizModeEnum::Manual->name,
            'starts_at' => now()->addMinutes(10),
            'ends_at' => null,
            'is_public' => true,
            'podium' => 3,
            'config' => [
                'max_users_per_quiz' => 1500,
                'time_per_question' => 10,
                'allow_lose_points' => false,
                'allow_negative_points' => false,
                'use_random_questions' => true,
                'show_ranking_between_questions' => true,
                'time_per_ranking' => 10,
                'show_correct_answer' => true,
                'play_audio_on_monitor' => true,
                'ask_for_email_at_the_end' => ChannelQuizConfigAskForEmailEnum::All->name,
                'multimedia_config' => [],
                'style_config' => [
                    'primary' => null,
                    'secondary' => null,
                    'text_primary' => null,
                    'text_secondary' => null,
                    'bg_primary' => null,
                    'bg_secondary' => null,
                    'btn_text_primary' => null,
                    'btn_text_secondary' => null,
                    'btn_bg_primary' => null,
                    'btn_bg_secondary' => null,
                    'register_label_text' => null,
                    'register_input_text' => null,
                    'progress_bar_bg' => null,
                    'question_text' => null,
                    'question_text_border' => null,
                    'answer_btn_text' => null,
                    'answer_btn_bg_selected' => null,
                    'answer_btn_bg_correct' => null,
                    'answer_btn_bg_incorrect' => null,
                    'ranking_text' => null,
                    'ranking_bg' => null,
                    'banner_btn_text' => null,
                    'banner_btn_bg' => null,
                ],
                'prize_configs' => [
                    [
                        'description' => 'Descripción del premio',
                        'from_position' => 1,
                        'to_position' => 3,
                        'image' => null,
                    ]
                ],
                'register_config_fields' => [
                    [
                        'label' => 'Nombre',
                        'type' => RegisterConfigFieldTypeEnum::DefaultName->name,
                        'validations' => ['required'],
                    ],
                ],
                'whitelist' => [
                    'name' => null,
                    'body' => null,
                    'field_name' => null,
                    'status' => WhitelistStatusEnum::Inactive->name,
                ]
            ],
            // 'groups' => [
            //     [
            //         'name' => 'Grupo A',
            //         'max_users' => 0,
            //         'whitelist' => [
            //             'name' => 'Whitelist Primera Partida',
            //             'body' => ["<EMAIL>","<EMAIL>","<EMAIL>"],
            //             'field_name' => RegisterConfigFieldTypeEnum::DefaultEmail->name,
            //             'status' => WhitelistStatusEnum::Active->name,
            //         ],
            //     ],
            //     [
            //         'name' => 'Grupo B',
            //         'max_users' => 0,
            //         'whitelist' => [
            //             'name' => 'Whitelist Primera Partida',
            //             'body' => ["<EMAIL>","<EMAIL>"],
            //             'field_name' => RegisterConfigFieldTypeEnum::DefaultEmail->name,
            //             'status' => WhitelistStatusEnum::Active->name,
            //         ],
            //     ]
            // ]
        ];

        if (!Quiz::exists()) {
            QuestionSetRepository::save($questionSetInput);
            QuizRepository::save($quizInput);
        }
    }
}
