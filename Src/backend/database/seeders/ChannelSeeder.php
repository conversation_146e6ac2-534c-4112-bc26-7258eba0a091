<?php

namespace Database\Seeders;

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Models\Channel;
use App\Repositories\ChannelRepository;
use Illuminate\Database\Seeder;

class ChannelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $channelInput = [
            'name' => 'TriviasFun',
            'slug' => 'triviasfun',
            'is_protected' => true,
            'config' => [
                'max_users_per_quiz' => 1500,
                'time_per_question' => 11,
                'allow_lose_points' => false,
                'allow_negative_points' => false,
                'use_random_questions' => true,
                'amount_random_questions' => null,
                'show_ranking_between_questions' => true,
                'time_per_ranking' => 11,
                'show_correct_answer' => true,
                'play_audio_on_monitor' => false,
                'ask_for_email_at_the_end' => ChannelQuizConfigAskForEmailEnum::All->name,
                'multimedia_config' => [],
                'style_config' => [
                    'primary' => null,
                    'secondary' => null,
                    'text_primary' => null,
                    'text_secondary' => null,
                    'bg_primary' => null,
                    'bg_secondary' => null,
                    'btn_text_primary' => null,
                    'btn_text_secondary' => null,
                    'btn_bg_primary' => null,
                    'btn_bg_secondary' => null,
                    'register_label_text' => null,
                    'register_input_text' => null,
                    'progress_bar_bg' => null,
                    'question_text' => null,
                    'question_text_border' => null,
                    'answer_btn_text' => null,
                    'answer_btn_bg_selected' => null,
                    'answer_btn_bg_correct' => null,
                    'answer_btn_bg_incorrect' => null,
                    'ranking_text' => null,
                    'ranking_bg' => null,
                    'banner_btn_text' => null,
                    'banner_btn_bg' => null,
                ],
                'register_config_fields' => [
                    [
                        'label' => 'Nombre',
                        'type' => RegisterConfigFieldTypeEnum::DefaultName->name,
                        'validations' => ['required'],
                    ]
                ]
            ]
        ];

        if (!Channel::whereSlug($channelInput['slug'])->exists()) {
            ChannelRepository::save($channelInput);
        }
    }
}
