<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ThemeTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        collect([
            [
                'id' => 1,
                'name' => 'Arte',
            ],
            [
                'id' => 2,
                'name' => 'Ciencia',
            ],
            [
                'id' => 3,
                'name' => 'Deportes',
            ],
        ])->each(function ($item) {
            DB::table('theme_tags')->updateOrInsert(['id' => $item['id']], $item);
        });
    }
}
