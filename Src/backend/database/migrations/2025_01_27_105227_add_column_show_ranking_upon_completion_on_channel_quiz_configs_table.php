<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->string('show_ranking_upon_completion')->nullable()->after('ask_for_email_at_the_end')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->dropColumn('show_ranking_upon_completion');
        });
    }
};
