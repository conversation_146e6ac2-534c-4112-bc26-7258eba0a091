<?php

use <PERSON>oPham\DynamoDb\DynamoDbClientInterface;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    protected DynamoDbClientInterface $dynamoDbClient;

    public function __construct()
    {
        $this->dynamoDbClient = App::make(DynamoDbClientInterface::class);
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->dynamoDbClient->getClient()->createTable([
            'TableName' => 'trivias_fun.synced_quizzes',
            'AttributeDefinitions' => [
                [
                    'AttributeName' => 'id',
                    'AttributeType' => 'N'
                ]
            ],
            'KeySchema' => [
                [
                    'AttributeName' => 'id',
                    'KeyType'       => 'HASH'
                ]
            ],
            'BillingMode' => 'PAY_PER_REQUEST'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableExists', [
            'TableName' => 'trivias_fun.synced_quizzes'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->dynamoDbClient->getClient()->deleteTable([
            'TableName' => 'trivias_fun.synced_quizzes'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableNotExists', [
            'TableName' => 'trivias_fun.synced_quizzes'
        ]);
    }
};
