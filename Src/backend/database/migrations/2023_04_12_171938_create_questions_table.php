<?php

use App\Enums\LanguageEnum;
use App\Enums\QuestionStatusEnum;
use App\Enums\QuestionTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->string('body');
            $table->boolean('is_public')->default(0);
            $table->enum('status', QuestionStatusEnum::names())->default(QuestionStatusEnum::Active->name);
            $table->enum('lang', LanguageEnum::names());
            $table->enum('type', QuestionTypeEnum::names());

            $table->foreignId('owner_id')
                ->nullable()
                ->constrained('system_users')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
