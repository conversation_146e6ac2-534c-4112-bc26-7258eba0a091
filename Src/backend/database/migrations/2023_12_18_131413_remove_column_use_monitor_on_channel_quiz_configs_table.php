<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->dropColumn('use_monitor');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->boolean('use_monitor');
        });
    }
};
