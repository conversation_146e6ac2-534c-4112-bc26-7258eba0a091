<?php

use App\Enums\QuizModeEnum;
use App\Enums\QuizStatusEnum;
use App\Enums\QuizTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quizzes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('pin')->nullable();
            $table->string('monitor_pin')->nullable();
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('ends_at')->nullable();
            $table->unsignedTinyInteger('podium')->nullable();
            $table->enum('type', QuizTypeEnum::names());
            $table->enum('status', QuizStatusEnum::names())->default(QuizStatusEnum::Active->name);
            $table->enum('mode', QuizModeEnum::names())->nullable();

            $table->foreignId('channel_id')
                ->constrained('channels')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('config_id')
                ->constrained('channel_quiz_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('question_set_id')
                ->constrained('questions_sets')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('prize_id')
                ->nullable()
                ->constrained('quiz_prizes')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['pin', 'channel_id']);
            $table->unique(['monitor_pin', 'channel_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quizzes');
    }
};
