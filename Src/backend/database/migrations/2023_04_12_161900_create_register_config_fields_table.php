<?php

use App\Enums\RegisterConfigFieldTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('register_config_fields', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('label');
            $table->enum('type', RegisterConfigFieldTypeEnum::names());
            $table->string('mask')->nullable();
            $table->json('validations')->nullable();
            $table->boolean('show_in_ranking')->default(0);
            $table->string('label_for_ranking')->nullable();

            $table->foreignId('config_id')
                ->nullable()
                ->constrained('channel_quiz_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('register_config_fields');
    }
};
