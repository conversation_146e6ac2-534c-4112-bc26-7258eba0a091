<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_quiz_profile_register_field_values', function (Blueprint $table) {
            $table->id();
            $table->string('value');

            $table->foreignId('profile_id')
                ->constrained('user_quiz_profiles')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->foreignId('field_id')
                ->constrained('register_config_fields')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->foreignId('quiz_id')
                ->constrained('quizzes')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->timestamps();

            $table->unique(['profile_id', 'field_id'], 'user_quiz_profile_register_field_values_uk1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_quiz_profile_register_field_values');
    }
};
