<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->unsignedInteger('amount_random_questions')->after('use_random_questions')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->dropColumn('amount_random_questions');
        });
    }
};
