<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->boolean('allow_lose_points')->after('message_secondary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->dropColumn('allow_lose_points');
        });
    }
};
