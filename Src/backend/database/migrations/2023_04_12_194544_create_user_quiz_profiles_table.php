<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_quiz_profiles', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_quiz_id')
                ->unique()
                ->constrained('user_quizzes')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_quiz_profiles');
    }
};
