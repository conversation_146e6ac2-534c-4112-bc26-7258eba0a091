<?php

use App\Enums\ChannelStatusEnum;
use App\Enums\LanguageEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('channels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('website_url')->nullable();
            $table->string('tyc_url')->nullable();
            $table->string('email')->nullable();
            $table->enum('status', ChannelStatusEnum::names())->default(ChannelStatusEnum::Active->name);
            $table->enum('lang', LanguageEnum::names())->default(LanguageEnum::Es->name);

            $table->foreignId('config_id')
                ->constrained('channel_quiz_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('system_user_id')
                ->nullable()
                ->constrained('system_users')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('channels');
    }
};
