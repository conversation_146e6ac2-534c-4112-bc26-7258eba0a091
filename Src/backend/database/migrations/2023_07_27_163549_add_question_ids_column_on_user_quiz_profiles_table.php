<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_quiz_profiles', function (Blueprint $table) {
            $table->json('question_ids')->after('user_quiz_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_quiz_profiles', function (Blueprint $table) {
            $table->dropColumn('question_ids');
        });
    }
};
