<?php

use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Enums\GiveawayStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('giveaways', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('quiz_id')->nullable()->constrained('quizzes')->restrictOnDelete();
            $table->enum('status', GiveawayStatusEnum::names())->default(GiveawayStatusEnum::Pending->name);
            $table->string('pin')->nullable();
            $table->string('color_primary')->nullable();
            $table->boolean('audio_enabled')->default(true);
            $table->integer('draw_duration_seconds', unsigned: true)->default(10);
            $table->enum('allowed_participants_mode', GiveawayAllowedParticipantsModeEnum::names())->default(GiveawayAllowedParticipantsModeEnum::All->name);
            $table->integer('min_correct_answers', unsigned: true)->nullable()->default(0);
            $table->json('participants')->nullable();
            $table->json('winners')->nullable();
            $table->json('black_list')->nullable();
            $table->timestamps();
        });

        Schema::create('giveaway_prizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('giveaway_id')->constrained('giveaways')->cascadeOnDelete();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('quantity', unsigned: true)->default(1);
            $table->integer('min_position', unsigned: true)->nullable();
            $table->integer('max_position', unsigned: true)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('giveaway_prizes');
        Schema::dropIfExists('giveaways');
    }
};
