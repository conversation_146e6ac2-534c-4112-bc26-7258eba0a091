<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedInteger('max_users')->default(0);

            $table->foreignId('quiz_id')
                ->constrained('quizzes')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->foreignId('whitelist_id')
                ->nullable()
                ->constrained('whitelists')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_groups');
    }
};
