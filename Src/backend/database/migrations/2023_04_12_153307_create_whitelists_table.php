<?php

use App\Enums\RegisterConfigFieldTypeEnum;
use App\Enums\WhitelistStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whitelists', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->json('body')->nullable();
            $table->enum('field_name', array_filter(RegisterConfigFieldTypeEnum::names(), fn ($value) => Str::startsWith($value, 'Default')))->nullable();
            $table->enum('status', WhitelistStatusEnum::names())->default(WhitelistStatusEnum::Active->name);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whitelists');
    }
};
