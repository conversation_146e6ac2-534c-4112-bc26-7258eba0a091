<?php

use App\Enums\GiveawayAllowedParticipantsModeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('giveaways', function (Blueprint $table) {
            $table->integer('max_top_ranking', unsigned: true)->nullable()->after('min_correct_answers');
            $table->string('message_primary')->after('draw_duration_seconds')->nullable();
            $table->string('message_secondary')->after('message_primary')->nullable();

            $table->enum('allowed_participants_mode', GiveawayAllowedParticipantsModeEnum::names())
                  ->default(GiveawayAllowedParticipantsModeEnum::All->name)
                  ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('giveaways', function (Blueprint $table) {
            $table->dropColumn('max_top_ranking');
            $table->dropColumn('message_primary');
            $table->dropColumn('message_secondary');
        });
    }
};
