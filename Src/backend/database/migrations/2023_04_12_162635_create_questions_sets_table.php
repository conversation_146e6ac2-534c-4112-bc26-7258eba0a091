<?php

use App\Enums\QuestionSetStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions_sets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->boolean('is_public')->default(0);
            $table->enum('status', QuestionSetStatusEnum::names())->default(QuestionSetStatusEnum::Active->name);

            $table->foreignId('owner_id')
                ->nullable()
                ->constrained('system_users')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions_sets');
    }
};
