<?php

use <PERSON>oPham\DynamoDb\DynamoDbClientInterface;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    protected DynamoDbClientInterface $dynamoDbClient;

    public function __construct()
    {
        $this->dynamoDbClient = App::make(DynamoDbClientInterface::class);
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->dynamoDbClient->getClient()->createTable([
            'TableName' => 'trivias_fun.synced_quiz_groups',
            'AttributeDefinitions' => [
                [
                    'AttributeName' => 'id',
                    'AttributeType' => 'N'
                ],
                [
                    'AttributeName' => 'quiz_id',
                    'AttributeType' => 'N'
                ]
            ],
            'KeySchema' => [
                [
                    'AttributeName' => 'id',
                    'KeyType'       => 'HASH'
                ]
            ],
            'GlobalSecondaryIndexes' => [
                [
                    'IndexName' => 'quiz_index',
                    'KeySchema' => [
                        [
                            'AttributeName' => 'quiz_id',
                            'KeyType'       => 'HASH'
                        ]
                    ],
                    'Projection' => [
                        'ProjectionType'  => 'ALL'
                    ]
                ]
            ],
            'BillingMode' => 'PAY_PER_REQUEST'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableExists', [
            'TableName' => 'trivias_fun.synced_quiz_groups'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->dynamoDbClient->getClient()->deleteTable([
            'TableName' => 'trivias_fun.synced_quiz_groups'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableNotExists', [
            'TableName' => 'trivias_fun.synced_quiz_groups'
        ]);
    }
};
