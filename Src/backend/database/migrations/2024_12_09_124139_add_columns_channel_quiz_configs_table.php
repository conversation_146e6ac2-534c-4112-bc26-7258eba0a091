<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\ChannelQuizConfigScoringModeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->string('only_correct_winning_message')->nullable()->after('ask_for_email_at_the_end')->default('');
            $table->string('only_correct_loser_message')->nullable()->after('only_correct_winning_message')->default('');
            $table->unsignedInteger('minimum_correct_answers')->nullable()->after('only_correct_loser_message')->default(0);
            $table->enum('scoring_mode', ChannelQuizConfigScoringModeEnum::names())->after('minimum_correct_answers')->default(ChannelQuizConfigScoringModeEnum::Traditional->name);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('channel_quiz_configs', function (Blueprint $table) {
            $table->dropColumn('only_correct_winning_message');
            $table->dropColumn('only_correct_loser_message');
            $table->dropColumn('minimum_correct_answers');
            $table->dropColumn('scoring_mode');
        });
    }
};
