<?php

use <PERSON>oPham\DynamoDb\DynamoDbClientInterface;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    protected DynamoDbClientInterface $dynamoDbClient;

    public function __construct()
    {
        $this->dynamoDbClient = App::make(DynamoDbClientInterface::class);
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->dynamoDbClient->getClient()->createTable([
            'TableName' => 'trivias_fun.user_quiz_progress',
            'AttributeDefinitions' => [
                [
                    'AttributeName' => 'id',
                    'AttributeType' => 'S'
                ],
                [
                    'AttributeName' => 'quiz_id',
                    'AttributeType' => 'N'
                ],
                [
                    'AttributeName' => 'user_id',
                    'AttributeType' => 'N'
                ],
            ],
            'KeySchema' => [
                [
                    'AttributeName' => 'id',
                    'KeyType'       => 'HASH',
                ]
            ],
            'GlobalSecondaryIndexes' => [
                [
                    'IndexName' => 'quiz_user_index',
                    'KeySchema' => [
                        [
                            'AttributeName' => 'quiz_id',
                            'KeyType'       => 'HASH'
                        ],
                        [
                            'AttributeName' => 'user_id',
                            'KeyType'       => 'RANGE'
                        ]
                    ],
                    'Projection' => [
                        'ProjectionType'  => 'ALL'
                    ]
                ]
            ],
            'BillingMode' => 'PAY_PER_REQUEST'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableExists', [
            'TableName' => 'trivias_fun.user_quiz_progress'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->dynamoDbClient->getClient()->deleteTable([
            'TableName' => 'trivias_fun.user_quiz_progress'
        ]);

        $this->dynamoDbClient->getClient()->waitUntil('TableNotExists', [
            'TableName' => 'trivias_fun.user_quiz_progress'
        ]);
    }
};
