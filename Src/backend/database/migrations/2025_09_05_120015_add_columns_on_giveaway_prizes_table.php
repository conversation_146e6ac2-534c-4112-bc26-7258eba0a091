<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('giveaway_prizes', function (Blueprint $table) {
            $table->integer('draw_duration_seconds', unsigned: true)->after('quantity')->default(10);
            $table->boolean('draw_all_at_once')->after('draw_duration_seconds')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('giveaway_prizes', function (Blueprint $table) {
            $table->dropColumn('draw_duration_seconds');
            $table->dropColumn('draw_all_at_once');
        });
    }
};
