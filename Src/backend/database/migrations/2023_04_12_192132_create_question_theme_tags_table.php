<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('question_theme_tags', function (Blueprint $table) {
            $table->id();

            $table->foreignId('question_id')
                ->constrained('questions')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->foreignId('theme_tag_id')
                ->constrained('theme_tags')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->timestamps();

            $table->unique(['question_id', 'theme_tag_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('question_theme_tags');
    }
};
