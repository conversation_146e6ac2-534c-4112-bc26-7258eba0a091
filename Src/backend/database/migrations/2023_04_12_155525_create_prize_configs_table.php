<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prize_configs', function (Blueprint $table) {
            $table->id();
            $table->string('description')->nullable();
            $table->unsignedTinyInteger('from_position')->nullable();
            $table->unsignedTinyInteger('to_position')->nullable();

            $table->foreignId('config_id')
                ->nullable()
                ->constrained('channel_quiz_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prize_configs');
    }
};
