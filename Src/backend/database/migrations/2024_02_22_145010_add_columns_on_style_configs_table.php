<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('style_configs', function (Blueprint $table) {
            $table->string('question_text')->after('progress_bar_bg')->nullable();
            $table->string('question_text_border')->after('question_text')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('style_configs', function (Blueprint $table) {
            $table->dropColumn('question_text');
            $table->dropColumn('question_text_border');
        });
    }
};
