<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('style_configs', function (Blueprint $table) {
            $table->id();
            $table->string('text_primary')->nullable();
            $table->string('text_secondary')->nullable();
            $table->string('bg_primary')->nullable();
            $table->string('bg_secondary')->nullable();
            $table->string('btn_text_primary')->nullable();
            $table->string('btn_text_secondary')->nullable();
            $table->string('btn_bg_primary')->nullable();
            $table->string('btn_bg_secondary')->nullable();
            $table->string('register_label_text')->nullable();
            $table->string('register_input_text')->nullable();
            $table->string('progress_bar_bg')->nullable();
            $table->string('answer_btn_text')->nullable();
            $table->string('answer_btn_bg_selected')->nullable();
            $table->string('answer_btn_bg_correct')->nullable();
            $table->string('answer_btn_bg_incorrect')->nullable();
            $table->string('ranking_text')->nullable();
            $table->string('ranking_bg')->nullable();
            $table->string('banner_btn_text')->nullable();
            $table->string('banner_btn_bg')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('style_configs');
    }
};
