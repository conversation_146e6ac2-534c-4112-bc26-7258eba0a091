<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_quiz_profiles', function (Blueprint $table) {
            $table->unsignedTinyInteger('current_step_id')->after('question_ids')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_quiz_profiles', function (Blueprint $table) {
            $table->dropColumn('current_step_id');
        });
    }
};
