<?php

use App\Enums\QuestionTypeMultiplierEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_quiz_progress', function (Blueprint $table) {
            $table->id();
            $table->unsignedSmallInteger('step_id');
            $table->enum('points_multiplier', QuestionTypeMultiplierEnum::names());
            $table->integer('points_base_earned');
            $table->integer('points_earned');
            $table->integer('points_before');
            $table->integer('points_now');

            $table->foreignId('user_quiz_id')
                ->constrained('user_quizzes')
                ->cascadeOnUpdate()
                ->cascadeOnDelete()
            ;

            $table->foreignId('question_id')
                ->constrained('questions')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('selected_answer_id')
                ->constrained('answers')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();

            $table->unique(['user_quiz_id', 'question_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_quiz_progress');
    }
};
