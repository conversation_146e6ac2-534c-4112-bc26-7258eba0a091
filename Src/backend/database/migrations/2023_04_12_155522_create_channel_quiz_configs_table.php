<?php

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('channel_quiz_configs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('max_users_per_quiz');
            $table->unsignedInteger('time_per_question');
            $table->string('message_primary')->nullable();
            $table->string('message_secondary')->nullable();
            $table->boolean('allow_negative_points');
            $table->boolean('use_monitor');
            $table->boolean('use_random_questions');
            $table->boolean('show_ranking_between_questions');
            $table->unsignedInteger('time_per_ranking')->nullable();
            $table->boolean('show_correct_answer');
            $table->boolean('play_audio_on_monitor');
            $table->enum('ask_for_email_at_the_end', ChannelQuizConfigAskForEmailEnum::names());

            $table->foreignId('multimedia_config_id')
                ->constrained('multimedia_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('style_config_id')
                ->constrained('style_configs')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->foreignId('whitelist_id')
                ->nullable()
                ->constrained('whitelists')
                ->cascadeOnUpdate()
                ->restrictOnDelete()
            ;

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('channel_quiz_configs');
    }
};
