<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('style_configs', function (Blueprint $table) {
            $table->string('primary')->after('id')->nullable();
            $table->string('secondary')->after('primary')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('style_configs', function (Blueprint $table) {
            $table->dropColumn('primary');
            $table->dropColumn('secondary');
        });
    }
};
