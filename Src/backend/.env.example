APP_NAME="Trivias Fun"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:10111
WEBAPP_URL=http://localhost:10002
BACKOFFICE_URL=http://localhost:10004

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
LOG_DISCORD_WEBHOOK_URL=

DB_CONNECTION=mysql
DB_HOST=database
DB_HOST_WRITE=database
DB_HOST_READ_1=database
DB_HOST_READ_2=database
DB_HOST_READ_3=database
DB_HOST_READ_4=database
DB_PORT=3306
DB_DATABASE=main
DB_USERNAME=admin
DB_PASSWORD=root

DYNAMODB_CONNECTION=local
DYNAMODB_ENDPOINT=http://dynamodb:8000
DYNAMODB_LOCAL_ENDPOINT=${DYNAMODB_ENDPOINT}
DYNAMODB_KEY=
DYNAMODB_SECRET=
DYNAMODB_REGION=localhost

BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

MEDIA_DISK=media
MEDIA_TMP_DISK=media_tmp
RANKINGS_DISK=rankings

S3_MEDIA_BUCKET=aws-triviasfun-backend-storage-prod
S3_MEDIA_TMP_BUCKET=aws-triviasfun-backend-storage-tmp-prod
S3_RANKINGS_BUCKET=aws-triviasfun-rankings

PUSHER_APP_ID=12345
PUSHER_APP_KEY=12345
PUSHER_APP_SECRET=12345
PUSHER_APP_PATH=/ws/
PUSHER_HOST=backend-server
PUSHER_PORT=80
PUSHER_SCHEME=http
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
