<?php

namespace App\Websockets;

use App\Models\Quiz;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuizChangedSocket implements ShouldBroadcast
{
    use Dispatchable, SerializesModels;

    public $afterCommit = true;

    /**
     * Create a new event instance.
     */
    public function __construct(
        // private Quiz $_quiz,
        private int $_quizId,
    ) { }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.Quiz.' . $this->_quizId),
        ];
    }

    public function broadcastWith()
    {
        return $this->_getSynced();
    }

    /* -------------------- */

    // private function _get()
    // {
    //     $itinerary = collect($this->_quiz->raw_itinerary)->only(['questionsCount', 'steps']);
    //     $itinerary['steps'] = collect($itinerary['steps'])->map(function ($step) {
    //         return collect($step)->except(['progress', 'ranking']);
    //     });

    //     return [
    //         'quiz' => [
    //             'itinerary' => $itinerary,
    //             'ranking' => $this->_quiz->ranking,
    //             'name' => $this->_quiz->name,
    //             'last_event' => $this->_quiz->last_event,
    //             'state' => $this->_quiz->state,
    //             'state_enum' => $this->_quiz->state_enum,
    //             'starts_at' => $this->_quiz->starts_at,
    //             'restarts_at' => $this->_quiz->restarts_at,
    //             'resuming_at' => $this->_quiz->resuming_at,
    //             'ends_at' => $this->_quiz->ends_at,
    //         ]
    //     ];
    // }

    private function _getSynced()
    {
        $quiz = \App\DynamoDbModels\SyncedQuiz::find($this->_quizId);

        if (!$quiz) {
            return;
        }

        $itinerary = collect($quiz->raw_itinerary)->only(['questionsCount', 'steps']);
        $itinerary['steps'] = collect($itinerary['steps'])->map(function ($step) {
            return collect($step)->except(['progress', 'ranking', 'question']);
        });

        $ranking = $quiz->ranking;

        return [
            'quiz' => [
                'itinerary' => $itinerary,
                'ranking' => $ranking,
                'name' => $quiz['name'],
                'last_event' => $quiz['last_event'],
                'state' => $quiz['state'],
                'state_enum' => $quiz['state_enum'],
                'starts_at' => $quiz['starts_at'],
                'restarts_at' => $quiz['restarts_at'],
                'resuming_at' => $quiz['resuming_at'],
                'ends_at' => $quiz['ends_at'],
            ]
        ];
    }
}
