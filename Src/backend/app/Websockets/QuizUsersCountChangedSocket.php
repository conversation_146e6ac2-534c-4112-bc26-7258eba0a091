<?php

namespace App\Websockets;

use App\Models\Quiz;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuizUsersCountChangedSocket implements ShouldBroadcast
{
    use Dispatchable, SerializesModels;

    public $afterCommit = true;

    /**
     * Create a new event instance.
     */
    public function __construct(
        // private Quiz $_quiz,
        private int $_quizId,
    ) { }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.Quiz.' . $this->_quizId),
        ];
    }

    public function broadcastWith()
    {
        return $this->_getSynced();
    }

    /* -------------------- */

    // private function _get()
    // {
    //     return [
    //         'quiz' => [
    //             'users_count' => $this->_quiz->users()->count(),
    //         ]
    //     ];
    // }

    private function _getSynced()
    {
        $quiz = \App\DynamoDbModels\SyncedQuiz::find($this->_quizId);

        if (!$quiz) {
            return;
        }

        return [
            'quiz' => [
                'users_count' => $quiz->users_count,
            ]
        ];
    }
}
