<?php

namespace App\Websockets;

use App\Models\Quiz;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuizRankingChangedSocket implements ShouldBroadcast
{
    use Dispatchable, SerializesModels;

    public $afterCommit = true;

    /**
     * Create a new event instance.
     */
    public function __construct(
        // private Quiz $_quiz,
        private int $_quizId,
    ) { }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('App.Models.Quiz.' . $this->_quizId),
        ];
    }

    public function broadcastWith()
    {
        return $this->_getSynced();
    }

    /* -------------------- */

    // private function _get()
    // {
    //     $itinerary = collect($this->_quiz->itinerary)->only(['steps']);
    //     $itinerary['steps'] = collect($itinerary['steps'])->map(function ($step) {
    //         return collect($step)->only(['id', 'type', 'ranking']);
    //     });

    //     return [
    //         'quiz' => [
    //             'itinerary' => $itinerary,
    //             'ranking' => $this->_quiz->ranking,
    //         ]
    //     ];
    // }

    private function _getSynced()
    {
        $quiz = \App\DynamoDbModels\SyncedQuiz::find($this->_quizId);

        if (!$quiz) {
            return;
        }

        $itineraryStepRankings = $quiz->itinerary_step_rankings;

        $itinerary = collect($quiz->raw_itinerary)->only(['steps']);
        $itinerary['steps'] = collect($itinerary['steps'])->map(function ($step) use ($itineraryStepRankings) {
            $step['ranking'] = collect($itineraryStepRankings)->firstWhere('step_id', $step['id']);

            return collect($step)->only(['id', 'type', 'ranking']);
        });

        $ranking = $quiz->ranking;

        return [
            'quiz' => [
                'itinerary' => $itinerary,
                'ranking' => $ranking,
            ]
        ];
    }
}
