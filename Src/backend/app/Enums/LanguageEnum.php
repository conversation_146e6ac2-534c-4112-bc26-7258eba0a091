<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum LanguageEnum
{
    use BaseEnum;

    case Es;
    case En;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Es => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Español',
                    'en' => 'Spanish',
                },
            ],
            self::En => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Inglés',
                    'en' => 'English',
                },
            ],
        };
    }
}
