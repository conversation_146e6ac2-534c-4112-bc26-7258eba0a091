<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum SystemUserTypeEnum
{
    use BaseEnum;

    case Administrator;
    case Client;
    case ClientChild;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Administrator => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Administrador',
                    'en' => 'Administrator',
                },
                'color' => 'orange',
            ],
            self::Client => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Cliente',
                    'en' => 'Client',
                },
                'color' => 'blue',
            ],
            self::ClientChild => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Usuario de cliente',
                    'en' => 'Client user',
                },
                'color' => 'blue',
            ],
        };
    }
}
