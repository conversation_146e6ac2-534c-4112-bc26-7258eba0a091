<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use App\Core\Response\ErrorEnumException;
use Illuminate\Support\Facades\Lang;

enum ErrorEnum
{
    use BaseEnum;

    case INVALID_CREDENTIALS_ERROR;
    case INACTIVE_APP_CLIENT_ERROR;
    case INACTIVE_USER_ERROR;
    case PATH_NOT_ALLOWED_ERROR;
    case ORIGIN_NOT_ALLOWED_ERROR;
    case NO_USER_EMAIL_ERROR;
    case NOT_USER_FOUND_WITH_EMAIL_ERROR;
    case NOT_USER_FOUND_IN_HASH;
    case EXPIRED_HASH_ERROR;
    case INVALID_HASH_TOKEN_ERROR;
    case ALREADY_VERIFIED_EMAIL_ADDRESS_ERROR;
    case INVALID_REQUEST_CONCEPT;
    case QUIZ_SET_EVENT_ERROR;
    case QUIZ_ACCESS_ERROR;
    case QUIZ_ACCESS_PIN_ERROR;
    case ALREADY_USER_QUIZ_PROFILE_ERROR;
    case NO_USER_QUIZ_PROFILE_ERROR;
    case ALREADY_USER_QUIZ_PROGRESS_ERROR;
    case QUIZ_MAX_USERS_LIMIT_REACHED_ERROR;
    case QUIZ_GROUP_MAX_USERS_LIMIT_REACHED_ERROR;
    case QUIZ_WHITELIST_ERROR;
    case QUIZ_SET_EVENT_NOT_ALLOWED_ERROR;
    case QUIZ_SET_DELETE_NOT_ALLOWED_BECAUSE_HAS_QUIZZES_ERROR;
    case CHANNEL_DELETE_PROTECTED_ERROR;
    case CHANNEL_UPDATE_SLUG_PROTECTED_ERROR;
    case INACTIVE_QUIZ_ERROR;
    case INACTIVE_CHANNEL_ERROR;
    case CHANNEL_DELETE_HAS_QUIZ_ERROR;
    case NO_QUIZ_ERROR;
    case NO_GIVEAWAY_ERROR;

    /* -------------------- */

    public function throw()
    {
        throw new ErrorEnumException($this);
    }

    /* -------------------- */

    public function value(array $args = []): array
    {
        return match($this) {
            self::INVALID_CREDENTIALS_ERROR => [
                'code' => 1,
                'status' => 401,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Las credenciales provistas son incorrectas.',
                    'en' => 'The provided credentials are incorrect.',
                }
            ],
            self::INACTIVE_APP_CLIENT_ERROR => [
                'code' => 2,
                'status' => 401,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El cliente provisto no se encuentra activo.',
                    'en' => 'The provided client is not active.',
                }
            ],
            self::INACTIVE_USER_ERROR => [
                'code' => 3,
                'status' => 401,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El usuario provisto no se encuentra activo.',
                    'en' => 'The provided user is not active.',
                }
            ],
            self::PATH_NOT_ALLOWED_ERROR => [
                'code' => 4,
                'status' => 403,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El endpoint solicitado no está permitido.',
                    'en' => 'The requested endpoint is not allowed.',
                }
            ],
            self::ORIGIN_NOT_ALLOWED_ERROR => [
                'code' => 5,
                'status' => 403,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El origen de la petición no está permitido.',
                    'en' => 'The origin of the request is not allowed.',
                }
            ],
            self::NO_USER_EMAIL_ERROR => [
                'code' => 6,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Este usuario no tiene una dirección de correo para enviar.',
                    'en' => 'This user does not have an email address to send.',
                }
            ],
            self::NOT_USER_FOUND_WITH_EMAIL_ERROR => [
                'code' => 7,
                'status' => 404,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se ha encontrado ningún usuario con esta dirección de correo.',
                    'en' => 'No user with this email address was found.',
                }
            ],
            self::NOT_USER_FOUND_IN_HASH => [
                'code' => 8,
                'status' => 404,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se ha encontrado ningún usuario correspondiente a este hash.',
                    'en' => 'No user found corresponding to this hash.',
                }
            ],
            self::EXPIRED_HASH_ERROR => [
                'code' => 9,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Este hash ha expirado.',
                    'en' => 'This hash has expired.',
                }
            ],
            self::INVALID_HASH_TOKEN_ERROR => [
                'code' => 10,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El token del hash es incorrecto o ha caducado.',
                    'en' => 'The hash token is incorrect or has expired.',
                }
            ],
            self::ALREADY_VERIFIED_EMAIL_ADDRESS_ERROR => [
                'code' => 11,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Esta dirección de correo ya ha sido verificada.',
                    'en' => 'This email address has already been verified.',
                }
            ],
            self::INVALID_REQUEST_CONCEPT => [
                'code' => 12,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Concepto de petición inválido.',
                    'en' => 'Invalid request concept.',
                }
            ],
            self::QUIZ_SET_EVENT_ERROR => [
                'code' => 13,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede establecer el evento solicitado.',
                    'en' => 'Cannot set requested event.',
                }
            ],
            self::QUIZ_ACCESS_ERROR => [
                'code' => 14,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No tiene acceso a esta partida.',
                    'en' => 'You do not have access to this quiz.',
                }
            ],
            self::QUIZ_ACCESS_PIN_ERROR => [
                'code' => 15,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Pin inválido.',
                    'en' => 'Invalid pin.',
                }
            ],
            self::ALREADY_USER_QUIZ_PROFILE_ERROR => [
                'code' => 16,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Ya existe un perfil registrado en esta partida.',
                    'en' => 'There is already a profile registered in this quiz.',
                }
            ],
            self::NO_USER_QUIZ_PROFILE_ERROR => [
                'code' => 17,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No tiene un perfil registrado en esta partida.',
                    'en' => 'You do not have a registered profile in this game.',
                }
            ],
            self::ALREADY_USER_QUIZ_PROGRESS_ERROR => [
                'code' => 18,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Ya se ha guardado el progreso de esta pregunta.',
                    'en' => 'The progress of this question has already been saved.',
                }
            ],
            self::QUIZ_MAX_USERS_LIMIT_REACHED_ERROR => [
                'code' => 19,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Se alcanzó el límite de jugadores para esta partida.',
                    'en' => 'The player limit has been reached for this quiz.',
                }
            ],
            self::QUIZ_GROUP_MAX_USERS_LIMIT_REACHED_ERROR => [
                'code' => 19,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Se alcanzó el límite de jugadores para este grupo.',
                    'en' => 'The player limit has been reached for this group.',
                }
            ],
            self::QUIZ_WHITELIST_ERROR => [
                'code' => 20,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'Los datos ingresados no están autorizados para permitir unirse a esta partida.',
                    'en' => 'The data entered is not authorized to allow joining this quiz.',
                }
            ],
            self::QUIZ_SET_EVENT_NOT_ALLOWED_ERROR => [
                'code' => 21,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede establecer el evento solicitado en este tipo de partida.',
                    'en' => 'Cannot set requested event in this quiz type.',
                }
            ],
            self::QUIZ_SET_DELETE_NOT_ALLOWED_BECAUSE_HAS_QUIZZES_ERROR => [
                'code' => 22,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede eliminar el set de preguntas provisto porque hay partidas configuradas con el mismo.',
                    'en' => 'The provided question set cannot be deleted because there are games configured with the same set.',
                }
            ],
            self::CHANNEL_DELETE_PROTECTED_ERROR => [
                'code' => 23,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede eliminar el canal provisto porque está protegido.',
                    'en' => 'The provided channel cannot be deleted because it is protected.',
                }
            ],
            self::CHANNEL_UPDATE_SLUG_PROTECTED_ERROR => [
                'code' => 23,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede actualizar el slug del canal provisto porque está protegido.',
                    'en' => 'The provided channel slug cannot be updated because it is protected.',
                }
            ],
            self::INACTIVE_QUIZ_ERROR => [
                'code' => 24,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'La partida provista no se encuentra activa.',
                    'en' => 'The provided quiz is not active.',
                }
            ],
            self::INACTIVE_CHANNEL_ERROR => [
                'code' => 25,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'El canal provisto no se encuentra activo.',
                    'en' => 'The provided channel is not active.',
                }
            ],
            self::CHANNEL_DELETE_HAS_QUIZ_ERROR => [
                'code' => 26,
                'status' => 400,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se puede eliminar el canal provisto porque tiene partidas asociadas.',
                    'en' => 'The provided channel cannot be deleted because it has associated quizzes.',
                }
            ],
            self::NO_QUIZ_ERROR => [
                'code' => 27,
                'status' => 404,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se ha encontrado ninguna partida.',
                    'en' => 'No quiz was found.',
                }
            ],
            self::NO_GIVEAWAY_ERROR => [
                'code' => 28,
                'status' => 404,
                'message' => match(Lang::getLocale()) {
                    'es' => 'No se ha encontrado ningún sorteo.',
                    'en' => 'No giveaway was found.',
                }
            ],
        };
    }
}
