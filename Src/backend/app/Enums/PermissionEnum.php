<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;

enum PermissionEnum
{
    use BaseEnum;

    case SystemUserGet;
    case SystemUserCreate;
    case SystemUserUpdate;
    case SystemUserDelete;
    case SystemUserActivate;
    case SystemUserDeactivate;
    case ChannelGet;
    case ChannelCreate;
    case ChannelUpdate;
    case ChannelDelete;
    case ChannelActivate;
    case ChannelDeactivate;
    case QuestionSetGet;
    case QuestionSetCreate;
    case QuestionSetUpdate;
    case QuestionSetDelete;
    case QuestionSetActivate;
    case QuestionSetDeactivate;
    case QuizGet;
    case QuizCreate;
    case QuizUpdate;
    case QuizDelete;
    case QuizActivate;
    case QuizDeactivate;
    case QuizPlay;
    case QuizPause;
    case UserQuizProfileGet;
    case UserQuizProfileDelete;
    case ReportQuizGet;
    case GiveawayGet;
    case GiveawayCreate;
    case GiveawayUpdate;
    case GiveawayDelete;

    /* -------------------- */

    public function value(): array
    {
        return [];
    }
}
