<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum QuizModeEnum
{
    use BaseEnum;

    case Manual;
    case Scheduled;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Manual => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Manual',
                    'en' => 'Manual',
                }
            ],
            self::Scheduled => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Programada',
                    'en' => 'Scheduled',
                }
            ],
        };
    }
}
