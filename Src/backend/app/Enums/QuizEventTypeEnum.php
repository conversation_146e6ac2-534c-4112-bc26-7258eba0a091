<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum QuizEventTypeEnum
{
    use BaseEnum;

    case Paused;
    case Playing;
    case Finished;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Paused => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Pausada',
                    'en' => 'Paused',
                },
            ],
            self::Playing => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Jugando',
                    'en' => 'Playing',
                },
            ],
            self::Finished => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Finalizada',
                    'en' => 'Finished',
                },
            ],
        };
    }
}
