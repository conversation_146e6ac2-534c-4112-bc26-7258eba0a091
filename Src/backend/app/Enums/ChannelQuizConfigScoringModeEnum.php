<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum ChannelQuizConfigScoringModeEnum
{
    use BaseEnum;

    case Traditional;
    case OnlyCorrect;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Traditional => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Tradicional',
                    'en' => 'Traditional',
                },
            ],
            self::OnlyCorrect => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Solo correctas',
                    'en' => 'Only Correct',
                },
            ],
        };
    }
}
