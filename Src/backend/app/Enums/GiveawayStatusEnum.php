<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum GiveawayStatusEnum
{
    use BaseEnum;

    case Pending;
    case InProgress;
    case Completed;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Pending => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Pendiente',
                    'en' => 'Pending',
                },
                'color' => '',
            ],
            self::InProgress => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'En progreso',
                    'en' => 'In progress',
                },
                'color' => 'blue',
            ],
            self::Completed => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Completado',
                    'en' => 'Completed',
                },
                'color' => 'green',
            ]
        };
    }
}