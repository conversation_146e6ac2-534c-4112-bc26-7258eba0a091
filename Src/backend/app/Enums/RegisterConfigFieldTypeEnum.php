<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum RegisterConfigFieldTypeEnum
{
    use BaseEnum;

    case DefaultName;
    case DefaultEmail;
    case DefaultDNI;

    /* -------------------- */

    case Text;
    case Checkbox;
    case Number;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::DefaultName => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'Nombre',
                    'en' => 'Name',
                },
            ],
            self::DefaultEmail => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'Email',
                    'en' => 'Email',
                },
            ],
            self::DefaultDNI => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'DNI',
                    'en' => 'DNI',
                },
                'label_generic' => match(Lang::getLocale()) {
                    'es' => 'Número',
                    'en' => 'Number',
                },
            ],
            /* -------------------- */
            self::Text => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'Texto',
                    'en' => 'Text',
                },
            ],
            self::Checkbox => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'Checkbox',
                    'en' => 'Checkbox',
                },
            ],
            self::Number => [
                '_name' => $this->name,
                'label' => match(Lang::getLocale()) {
                    'es' => 'Número',
                    'en' => 'Number',
                },
            ]
        };
    }
}
