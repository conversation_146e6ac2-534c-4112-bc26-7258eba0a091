<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum QuizTypeEnum: string
{
    use BaseEnum;

    case Live = 'Live';
    case OnDemand = 'OnDemand';

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Live => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Vivo',
                    'en' => 'Live',
                }
            ],
            self::OnDemand => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'On demand',
                    'en' => 'On demand',
                }
            ],
        };
    }
}
