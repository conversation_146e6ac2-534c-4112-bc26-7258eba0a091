<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum GiveawayAllowedParticipantsModeEnum
{
    use BaseEnum;

    case All;
    case NonWinners;
    case CorrectAnswers;
    case TopRanking;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::All => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Todos',
                    'en' => 'All',
                }
            ],
            self::NonWinners => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Jugadores que no ganaron premio en la partida',
                    'en' => 'Players that did not win the prize in the quiz',
                }
            ],
            self::CorrectAnswers => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Jugadores que respondieron correctamente',
                    'en' => 'Players that answered correctly',
                }
            ],
            self::TopRanking => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Jugadores del top del ranking',
                    'en' => 'Players in the top of the ranking',
                }
            ],
        };
    }
}