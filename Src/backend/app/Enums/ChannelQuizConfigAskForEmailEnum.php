<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum ChannelQuizConfigAskForEmailEnum
{
    use BaseEnum;

    case None;
    case All;
    case Winners;

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::None => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Ninguno',
                    'en' => 'None',
                },
            ],
            self::All => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Todos',
                    'en' => 'All',
                },
            ],
            self::Winners => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Ganadores',
                    'en' => 'Winners',
                },
            ],
        };
    }
}
