<?php

namespace App\Enums;

use App\Core\Bases\BaseEnum;
use Illuminate\Support\Facades\Lang;

enum QuestionTypeEnum: string
{
    use BaseEnum;

    case Normal = 'Normal';
    case Double = 'Double';
    case Bomb = 'Bomb';
    case Survey = 'Survey';

    /* -------------------- */

    public function value(): array
    {
        return match($this) {
            self::Normal => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Normal',
                    'en' => 'Normal',
                },
                'multiplier' => QuestionTypeMultiplierEnum::One->value()
            ],
            self::Double => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Doble',
                    'en' => 'Double',
                },
                'multiplier' => QuestionTypeMultiplierEnum::Two->value()
            ],
            self::Bomb => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Bomba',
                    'en' => 'Bomb',
                },
                'multiplier' => QuestionTypeMultiplierEnum::Five->value()
            ],
            self::Survey => [
                'label' => match(Lang::getLocale()) {
                    'es' => 'Encuesta',
                    'en' => 'Survey',
                },
                'multiplier' => QuestionTypeMultiplierEnum::Zero->value()
            ],
        };
    }
}
