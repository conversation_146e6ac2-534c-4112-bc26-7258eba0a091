<?php

namespace App\Features\Itinerary\Enums;

use App\Core\Bases\BaseEnum;

enum ItineraryStepTypeEnum: string
{
    use BaseEnum;

    case Lobby = 'Lobby';
    case Question = 'Question';
    case QuestionResults = 'QuestionResults';
    case Ranking = 'Ranking';
    case FinalRanking = 'FinalRanking';
    case PreparingResults = 'PreparingResults';
    case Results = 'Results';
    case FinalResults = 'FinalResults';

    /* -------------------- */

    public function value(): array
    {
        return [];
    }
}
