<?php

namespace App\Features\Itinerary;

use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use App\Enums\QuizTypeEnum;
use App\Models\Question;
use App\Models\Quiz;
use App\Models\User;
use App\Models\UserQuizProfile;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class Itinerary
{
    public Collection $steps;
    public int $questionsCount;
    public QuizTypeEnum $quizType;
    public ?ItineraryStep $currentStep;

    private ?UserQuizProfile $_userProfile;

    public function __construct(
        private Quiz $_quiz,
        private ?User $_user = null,
    )
    {
        $this->steps = collect();
        $this->questionsCount = 0;
        $this->quizType = $this->_quiz->type;

        if ($this->_user) {
            $this->_userProfile = $this->_user->profile_on_quiz($this->_quiz->id)->first();
        }

        $this->_create();
    }

    /* -------------------- */

    private function _create()
    {
        if (!$this->_quiz->questions()->exists()) {
            return;
        }

        $this->_addStep(ItineraryStepTypeEnum::Lobby);

        if ($this->_quiz->type->is(QuizTypeEnum::Live) || !$this->_quiz->config->use_random_questions) {
            $questions = $this->_quiz->questions()
                ->withSortedAnswers()
                ->get()
                ->makeHidden(['media', 'pivot']);
        } else {
            $questionIds = $this->_userProfile->question_ids ?? $this->_quiz->getRandomQuestionIds();

            $questions = $this->_quiz->questions()
                ->withSortedAnswers()
                ->whereIn('questions.id', $questionIds)
                ->get()
                ->makeHidden(['media', 'pivot'])
                ->sortBy(function ($question) use ($questionIds) {
                    return array_search($question->id, $questionIds);
                });
        }

        foreach ($questions as $question) {
            $this->_addStep(ItineraryStepTypeEnum::Question, $question);
            $this->_addStep(ItineraryStepTypeEnum::QuestionResults, $question);

            if ($this->_quiz->config->show_ranking_between_questions && $questions->last()->id !== $question->id) {
                $this->_addStep(ItineraryStepTypeEnum::Ranking, $question);
            }
        }

        if ($this->_quiz->type->is(QuizTypeEnum::Live)) {
            $this->_addStep(ItineraryStepTypeEnum::FinalRanking);
        }

        if ($this->_quiz->type->is(QuizTypeEnum::Live)) {
            $this->_addStep(ItineraryStepTypeEnum::PreparingResults);
            $this->_addStep(ItineraryStepTypeEnum::FinalResults);
        } else {
            $this->_addStep(ItineraryStepTypeEnum::Results);
            $this->_addStep(ItineraryStepTypeEnum::PreparingResults);
            $this->_addStep(ItineraryStepTypeEnum::FinalResults);
        }
    }

    /* -------------------- */

    private function _addStep(ItineraryStepTypeEnum $type, ?Question $question = null)
    {
        $lastStep = $this->steps->last();

        $step = new ItineraryStep(
            ($lastStep->id ?? 0) + 1,
            $type,
        );

        $step->question = $question;

        switch ($this->_quiz->type) {
            case QuizTypeEnum::Live: $this->_addStepLive($step, $lastStep); break;
            case QuizTypeEnum::OnDemand: $this->_addStepOnDemand($step, $lastStep); break;

            default: break;
        }
    }

    private function _addStepLive(ItineraryStep $step, ?ItineraryStep $lastStep = null)
    {
        switch ($step->type) {
            case ItineraryStepTypeEnum::Lobby: {
                $step->startsAt = Carbon::now()->subMinute();

                $this->_quiz->isPlaying() || $this->_quiz->isFinished()
                    ? $step->endsAt = $this->_quiz->restarts_at->clone()
                    : $step->standBy = !$this->_quiz->isPlaying();

                break;
            }

            case ItineraryStepTypeEnum::Question: {
                $this->questionsCount++;

                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds($this->_quiz->config->time_per_question);
                }

                break;
            }

            case ItineraryStepTypeEnum::QuestionResults: {
                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds(5);
                }

                break;
            }

            case ItineraryStepTypeEnum::Ranking: {
                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds($this->_quiz->config->time_per_ranking);
                }

                break;
            }

            case ItineraryStepTypeEnum::FinalRanking: {
                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds($this->_quiz->config->time_per_ranking);
                }

                break;
            }

            case ItineraryStepTypeEnum::PreparingResults: {
                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds(5);
                }

                break;
            }

            case ItineraryStepTypeEnum::FinalResults: {
                if ($lastStep->endsAt) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds(60);
                }

                break;
            }
        }

        $step->currentQuestionCount = $this->questionsCount;

        $this->steps->add($step);
    }

    private function _addStepOnDemand(ItineraryStep $step, ?ItineraryStep $lastStep = null)
    {
        switch ($step->type) {
            case ItineraryStepTypeEnum::Lobby: {
                $step->duration = 3;
                $step->trackStep = true;

                break;
            }

            case ItineraryStepTypeEnum::Question: {
                $this->questionsCount++;

                $step->duration = $this->_quiz->config->time_per_question;
                $step->trackStep = true;

                break;
            }

            case ItineraryStepTypeEnum::QuestionResults: {
                $step->duration = 5;
                $step->trackStep = true;

                break;
            }

            case ItineraryStepTypeEnum::Ranking: {
                $step->trackStep = true;

                break;
            }

            case ItineraryStepTypeEnum::Results: {
                $step->endsAt = $this->_quiz->ends_at->clone();
                $step->trackStep = true;

                break;
            }

            case ItineraryStepTypeEnum::PreparingResults: {
                if ($lastStep) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds(5);
                }

                break;
            }

            case ItineraryStepTypeEnum::FinalResults: {
                if ($lastStep) {
                    $step->startsAt = $lastStep->endsAt->clone();
                    $step->endsAt = $step->startsAt->clone()->addSeconds(600);
                }

                break;
            }
        }

        $step->currentQuestionCount = $this->questionsCount;

        $this->steps->add($step);
    }
}
