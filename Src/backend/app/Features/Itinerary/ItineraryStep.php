<?php

namespace App\Features\Itinerary;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use App\Models\Question;
use App\Models\UserQuizProgress;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ItineraryStep
{

    public function __construct(
        public int $id,
        public ItineraryStepTypeEnum $type,
        public int $currentQuestionCount = 1,
        public bool $standBy = false,
        public bool $trackStep = false,
        public ?Carbon $startsAt = null,
        public ?Carbon $endsAt = null,
        public ?int $duration = null,
        public ?Question $question = null,
        public ?UserQuizProgress $progress = null,
        public ?Collection $ranking = null,
    )
    {
        //
    }
}
