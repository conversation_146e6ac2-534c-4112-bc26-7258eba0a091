<?php

namespace App\Http\Requests\QuestionSet;

use App\Core\Bases\BaseRequest;

class QuestionSetUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(int $questionSetId, ?array $params = []): array
    {
        $rules = QuestionSetCreateRequest::rules($questionSetId);

        return $rules;
    }
}
