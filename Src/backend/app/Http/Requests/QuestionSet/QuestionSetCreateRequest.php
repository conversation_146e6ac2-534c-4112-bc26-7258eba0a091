<?php

namespace App\Http\Requests\QuestionSet;

use App\Enums\QuestionTypeEnum;
use App\Core\Bases\BaseRequest;
use Illuminate\Validation\Rule;

class QuestionSetCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $questionSetId = null, ?array $params = []): array
    {
        return [
            'name' => ['bail', 'required', 'string'],
            'is_public' => ['bail', 'required', 'boolean'],
            'theme_tags' => ['bail', 'nullable', 'array'],
            'theme_tags.*.id' => ['bail', 'nullable'],
            'theme_tags.*.name' => ['bail', 'nullable'],
            'use_import_file' => ['bail', 'nullable', 'boolean'],
            'import_file' => ['bail', 'required_if:use_import_file,true'],
            'import_file.id' => ['bail', 'required_if:use_import_file,true', 'exists:App\Models\Media,id'],
            'questions' => ['bail', 'required', 'array'],
            'questions.*.id' => ['bail', 'nullable', 'numeric'],
            'questions.*.body' => ['bail', 'required', 'max:120'],
            'questions.*.is_public' => ['bail', 'required', 'boolean'],
            'questions.*.type' => ['bail', 'required', Rule::in(QuestionTypeEnum::names())],
            'questions.*.pivot' => ['bail', 'nullable'],
            'questions.*.pivot.order' => ['bail', 'required', 'numeric'],
            'questions.*.image' => ['bail', 'nullable'],
            'questions.*.image_url' => ['bail', 'nullable', 'url'],
            'questions.*.audio' => ['bail', 'nullable'],
            'questions.*.audio_url' => ['bail', 'nullable', 'url'],
            'questions.*.answers' => ['bail'],
            'questions.*.answers.*.body' => ['bail', 'required'],
            'questions.*.answers.*.is_correct' => ['bail', 'required', 'boolean'],
        ];
    }
}
