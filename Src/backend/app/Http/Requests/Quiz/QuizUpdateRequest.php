<?php

namespace App\Http\Requests\Quiz;

use App\Core\Bases\BaseRequest;

class QuizUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(int $quizId, ?array $params = []): array
    {
        $rules = QuizCreateRequest::rules($quizId);

        return $rules;
    }
}
