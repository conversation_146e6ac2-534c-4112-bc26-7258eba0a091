<?php

namespace App\Http\Requests\Quiz;

use App\Core\Bases\BaseRequest;

class QuizUserProgressUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?array $params = []): array
    {
        return [
            'step_id' => ['bail', 'required', 'numeric'],
            'points_before' => ['bail', 'required', 'numeric'],
            'points_earned' => ['bail', 'required', 'numeric'],
            'allow_lose_points' => ['bail', 'required', 'boolean'],
            'allow_negative_points' => ['bail', 'required', 'boolean'],
            'question' => ['bail', 'required'],
            'question.id' => ['bail', 'nullable', 'numeric'],
            'selected_answer' => ['bail', 'required'],
            'selected_answer.id' => ['bail', 'nullable', 'numeric'],
            'selected_answer.isCorrect' => ['bail', 'nullable', 'boolean'],
            'has_websocket_connection' => ['bail', 'nullable', 'boolean'],
        ];
    }
}
