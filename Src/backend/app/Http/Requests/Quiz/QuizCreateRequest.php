<?php

namespace App\Http\Requests\Quiz;

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use App\Enums\ChannelQuizConfigScoringModeEnum;
use App\Enums\QuizModeEnum;
use App\Enums\QuizTypeEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Core\Bases\BaseRequest;
use App\Models\Channel;
use App\Models\Quiz;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;

class QuizCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $quizId = null, ?array $params = []): array
    {
        $channelExistsRule = Rule::modelExists(Channel::class, 'id', function (Builder $query) use ($quizId) {
            $query->when($quizId, function ($query) use ($quizId) {
                    return $query->where('id', Quiz::find($quizId)->channel_id);
                });
        });

        return [
            'name' => ['bail', 'required', 'string'],
            'public_name' => ['bail', 'nullable', 'string'],
            'channel' => ['bail', 'required'],
            'channel.id' => ['bail', 'required', $channelExistsRule],
            'question_set' => ['bail', 'required'],
            'question_set.id' => ['bail', 'required'],
            'type' => ['bail', 'required', Rule::in(QuizTypeEnum::names())],
            'mode' => ['bail', 'nullable', Rule::in(QuizModeEnum::names())],
            'is_public' => ['bail', 'required', 'boolean'],
            'starts_at' => ['bail', 'required'],
            'ends_at' => ['bail', 'nullable'],
            'podium' => ['bail', 'nullable', 'numeric'],
            'config_mode' => ['bail', 'required', 'string'],
            'config' => ['bail', 'required'],
            'config.max_users_per_quiz' => ['bail', 'required', 'numeric'],
            'config.time_per_question' => ['bail', 'required', 'numeric'],
            'config.message_primary' => ['bail', 'nullable', 'string'],
            'config.message_secondary' => ['bail', 'nullable', 'string'],
            'config.allow_lose_points' => ['bail', 'required', 'boolean'],
            'config.allow_negative_points' => ['bail', 'required', 'boolean'],
            'config.use_random_questions' => ['bail', 'required', 'boolean'],
            'config.amount_random_questions' => ['bail', 'nullable', 'numeric'],
            'config.show_ranking_between_questions' => ['bail', 'required', 'boolean'],
            'config.time_per_ranking' => ['bail', 'nullable', 'numeric'],
            'config.show_correct_answer' => ['bail', 'required', 'boolean'],
            'config.play_audio_on_monitor' => ['bail', 'required', 'boolean'],
            'config.ask_for_email_at_the_end' => ['bail', 'required', Rule::in(ChannelQuizConfigAskForEmailEnum::names())],
            'config.scoring_mode' => ['bail', 'required', Rule::in(ChannelQuizConfigScoringModeEnum::names())],
            'config.show_ranking_upon_completion' => ['bail', 'nullable', 'boolean'],
            'config.only_correct_winning_message' => ['bail', 'nullable', 'string'],
            'config.only_correct_loser_message' => ['bail', 'nullable', 'string'],
            'config.minimum_correct_answers' => ['bail', 'required', 'numeric'],
            'config.multimedia_config' => ['bail', 'required'],
            'config.multimedia_config.logo' => ['bail', 'nullable'],
            'config.multimedia_config.logo_link' => ['bail', 'nullable', 'url'],
            'config.multimedia_config.bg_video' => ['bail', 'nullable'],
            'config.multimedia_config.bg_image' => ['bail', 'nullable'],
            'config.multimedia_config.monitor_bg_video' => ['bail', 'nullable'],
            'config.multimedia_config.monitor_bg_image' => ['bail', 'nullable'],
            'config.multimedia_config.banner' => ['bail', 'nullable'],
            'config.multimedia_config.banner_link' => ['bail', 'nullable', 'url'],
            'config.style_config' => ['bail', 'required'],
            'config.style_config.css_file' => ['bail', 'nullable', 'string'],
            'config.style_config.primary' => ['bail', 'nullable', 'string'],
            'config.style_config.secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.text_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.text_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.bg_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.bg_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_text_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_text_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_bg_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_bg_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.register_label_text' => ['bail', 'nullable', 'string'],
            'config.style_config.register_input_text' => ['bail', 'nullable', 'string'],
            'config.style_config.progress_bar_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.question_text' => ['bail', 'nullable', 'string'],
            'config.style_config.question_text_border' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_text' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_selected' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_correct' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_incorrect' => ['bail', 'nullable', 'string'],
            'config.style_config.ranking_text' => ['bail', 'nullable', 'string'],
            'config.style_config.ranking_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.banner_btn_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.banner_btn_text' => ['bail', 'nullable', 'string'],
            'config.whitelist' => ['bail', 'required'],
            'config.whitelist.name' => ['bail', 'required_with:config.whitelist.body'],
            'config.whitelist.body' => ['bail', 'nullable'],
            'config.whitelist.field_name' => ['bail', 'required_with:config.whitelist.body'],
            'config.prize_configs' => ['bail', 'required', 'array'],
            'config.prize_configs.*.id' => ['bail', 'nullable'],
            'config.prize_configs.*.description' => ['bail', 'nullable', 'string'],
            'config.prize_configs.*.from_position' => ['bail', 'nullable', 'numeric'],
            'config.prize_configs.*.to_position' => ['bail', 'nullable', 'numeric'],
            'config.prize_configs.*.image' => ['bail', 'nullable'],
            'config.register_config_fields' => ['bail', 'nullable', 'array'],
            'config.register_config_fields.*.id' => ['bail', 'nullable'],
            'config.register_config_fields.*.label' => ['bail', 'required', 'string'],
            'config.register_config_fields.*.type' => ['bail', 'required', Rule::in(RegisterConfigFieldTypeEnum::names())],
            'config.register_config_fields.*.validations' => ['bail', 'nullable'],
            'config.register_config_fields.*.show_in_ranking' => ['bail', 'nullable', 'boolean'],
            'config.register_config_fields.*.label_for_ranking' => ['bail', 'nullable', 'string'],
            'groups' => ['bail', 'nullable', 'array'],
            'groups.*.id' => ['bail', 'nullable'],
            'groups.*.name' => ['bail', 'required', 'string'],
            'groups.*.max_users' => ['bail', 'required', 'numeric'],
            'groups.*.whitelist' => ['bail', 'required'],
            'groups.*.whitelist.name' => ['bail', 'required', 'string'],
            'groups.*.whitelist.body' => ['bail', 'nullable'],
            'groups.*.whitelist.field_name' => ['bail', 'required', 'string'],
        ];
    }
}
