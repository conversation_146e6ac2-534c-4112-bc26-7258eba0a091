<?php

namespace App\Http\Requests\Quiz;

use App\Core\Bases\BaseRequest;;

class QuizUserSetEndEmailRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?array $params = []): array
    {
        return [
            'end_email' => ['bail', 'required', 'email'],
        ];
    }
}
