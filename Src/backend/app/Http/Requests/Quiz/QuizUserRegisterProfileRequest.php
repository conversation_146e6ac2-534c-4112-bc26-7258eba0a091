<?php

namespace App\Http\Requests\Quiz;

use App\Enums\RegisterConfigFieldTypeEnum;
use App\Core\Bases\BaseRequest;
use App\DynamoDbModels\SyncedQuiz;
use App\Enums\WhitelistStatusEnum;
use Closure;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\Rule;

class QuizUserRegisterProfileRequest extends BaseRequest
{
    private static ?array $_customMessages = [];

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(int $quizId, ?array $params = []): array
    {
        $rules = [];
        $quiz = SyncedQuiz::findOrFail($quizId);
        $registerConfigFields = collect($quiz->config['register_config_fields']);
        $whitelist = $quiz->config['whitelist'];

        if ($registerConfigFields->some(fn ($field) => in_array('unique', $field['validations']))) {
            $profiles = $quiz->user_profiles;
        }

        foreach ($registerConfigFields as $field) {
            $validations = $field['validations'];

            if (!in_array('required', $validations)) {
                $validations[] = 'nullable';
            }

            if (in_array('unique', $validations)) {
                $validations[array_search('unique', $validations)] = self::_uniqueRule($profiles, $field);
            }

            if ($whitelist && $whitelist['status'] === WhitelistStatusEnum::Active->name) {
                if ($whitelist['field_name'] === RegisterConfigFieldTypeEnum::DefaultEmail->name && $field['type'] === RegisterConfigFieldTypeEnum::DefaultEmail->name) {
                    $validations[] = self::_inRule($whitelist, $field);
                }
                else if ($whitelist['field_name'] === RegisterConfigFieldTypeEnum::DefaultDNI->name && $field['type'] === RegisterConfigFieldTypeEnum::DefaultDNI->name) {
                    $validations[] = self::_inRule($whitelist, $field);
                }
            }

            $rules[$field['name']] = $validations;
        }

        $rules['has_websocket_connection'] = ['bail', 'required', 'boolean'];

        return $rules;
    }

    public static function customMessages(): array
    {
        return self::$_customMessages;
    }

    /* -------------------- */

    private static function _uniqueRule($profiles, $field)
    {
        if (!$profiles) return;

        $uniqueRule = function (string $attribute, mixed $value, Closure $fail) use ($profiles, $field) {
            foreach ($profiles as $profile) {
                foreach ($profile['register_field_values'] as $fieldValue) {
                    if ($fieldValue['field']['name'] === $field['name'] && $fieldValue['value'] === $value) {
                        $fail('validation.unique')->translate();
                    }
                }
            }
        };

        return $uniqueRule;
    }

    private static function _inRule($whitelist, $field)
    {
        if (in_array($field['type'], [RegisterConfigFieldTypeEnum::DefaultEmail->name, RegisterConfigFieldTypeEnum::DefaultDNI->name])) {
            $message = match(Lang::getLocale()) {
                'es' => ' no autorizado para esta partida.',
                'en' => ' not authorized for this quiz.',
            };

            self::$_customMessages[$field['name'] . '.in'] = $whitelist['field_name'] === RegisterConfigFieldTypeEnum::DefaultDNI->name
                ? $whitelist['field_name_enum']['label_generic'] . $message
                : $whitelist['field_name_enum']['label'] . $message
                ;
        }

        $inRule = function (string $attribute, mixed $value, Closure $fail) use ($whitelist, $field) {
            if (!in_array($value, $whitelist['body'], true)) {
                $fail(self::$_customMessages[$field['name'] . '.in']);
            }
        };

        return $inRule;
    }
}
