<?php

namespace App\Http\Requests\SystemUser;

use App\Enums\SystemUserTypeEnum;
use App\Core\Bases\BaseRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class SystemUserCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?array $params = []): array
    {
        return [
            'first_name' => ['bail', 'required', 'string'],
            'last_name' => ['bail', 'required', 'string'],
            'email' => ['bail', 'required', 'email', 'unique:system_users,email'],
            'type' => ['bail', 'nullable', Rule::in(SystemUserTypeEnum::names())],
            'channels' => ['bail', 'nullable', 'array'],
            'channels.*.id' => ['bail', 'nullable', 'exists:App\Models\Channel,id'],
            'password_input_type' => ['bail', 'required', 'in:random,manual'],
            'password' => ['bail', 'required_if:password_input_type,manual', 'string', Password::min(6)],
            'password_confirmation' => ['bail', 'required_if:password_input_type,manual', 'string', 'same:password', Password::min(6)],
            // 'picture' => ['bail', 'nullable', 'file', File::types(['jpg', 'png', 'webp'])->max(1024)],
        ];
    }
}
