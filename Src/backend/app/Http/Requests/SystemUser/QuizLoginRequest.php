<?php

namespace App\Http\Requests\SystemUser;

use App\Core\Bases\BaseRequest;

class QuizLoginRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?array $params = []): array
    {
        return [
            'id' => ['bail', 'required', 'numeric'],
            'monitor_pin' => ['bail', 'required', 'string'],
        ];
    }
}
