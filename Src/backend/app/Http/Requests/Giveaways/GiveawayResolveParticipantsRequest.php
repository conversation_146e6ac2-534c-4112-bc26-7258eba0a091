<?php

namespace App\Http\Requests\Giveaways;

use App\Core\Bases\BaseRequest;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use Illuminate\Validation\Rule;

class GiveawayResolveParticipantsRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $giveawayId = null, ?array $params = []): array
    {
        return [
            'file' => ['bail', 'nullable'],
            'quiz_id' => ['bail', 'nullable', 'exists:quizzes,id'],
            'allowed_participants_mode' => ['bail', 'nullable', Rule::in(GiveawayAllowedParticipantsModeEnum::names())],
            'min_correct_answers' => ['bail', 'nullable', 'numeric'],
            'max_top_ranking' => ['bail', 'nullable', 'numeric'],
        ];
    }
}
