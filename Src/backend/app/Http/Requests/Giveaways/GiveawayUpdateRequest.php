<?php

namespace App\Http\Requests\Giveaways;

use App\Core\Bases\BaseRequest;

class GiveawayUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(int $giveawayId, ?array $params = []): array
    {
        $rules = GiveawayCreateRequest::rules($giveawayId);

        return $rules;
    }
}
