<?php

namespace App\Http\Requests\Giveaways;

use App\Core\Bases\BaseRequest;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use Illuminate\Validation\Rule;

class GiveawayCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $giveawayId = null, ?array $params = []): array
    {
        return [
            'name' => ['bail', 'required', 'string'],
            'quiz' => ['bail', 'nullable'],
            'quiz.id' => ['bail', 'nullable', 'exists:quizzes,id'],
            'channel' => ['bail', 'nullable'],
            'channel.id' => ['bail', 'nullable', 'exists:channels,id'],
            'allowed_participants_mode' => ['bail', 'nullable', Rule::in(GiveawayAllowedParticipantsModeEnum::names())],
            'min_correct_answers' => ['bail', 'nullable', 'numeric'],
            'max_top_ranking' => ['bail', 'nullable', 'numeric'],
            'audio_enabled' => ['bail', 'nullable', 'boolean'],
            'message_primary' => ['bail', 'nullable', 'string'],
            'message_secondary' => ['bail', 'nullable', 'string'],
            'prizes' => ['bail', 'required', 'array'],
            'prizes.*.id' => ['bail', 'nullable'],
            'prizes.*.name' => ['bail', 'required', 'string'],
            'prizes.*.description' => ['bail', 'nullable', 'string'],
            'prizes.*.quantity' => ['bail', 'required', 'numeric'],
            'prizes.*.draw_duration_seconds' => ['bail', 'required', 'numeric'],
            'prizes.*.draw_all_at_once' => ['bail', 'required', 'boolean'],
            'prizes.*.min_position' => ['bail', 'nullable', 'numeric'],
            'prizes.*.max_position' => ['bail', 'nullable', 'numeric'],
            'prizes.*.image' => ['bail', 'nullable'],
            'style_config' => ['bail', 'required'],
            'style_config.primary' => ['bail', 'nullable', 'string'],
            'style_config.secondary' => ['bail', 'nullable', 'string'],
            'style_config.text_primary' => ['bail', 'nullable', 'string'],
            'style_config.text_secondary' => ['bail', 'nullable', 'string'],
            'style_config.bg_primary' => ['bail', 'nullable', 'string'],
            'style_config.bg_secondary' => ['bail', 'nullable', 'string'],
            'style_config.btn_text_primary' => ['bail', 'nullable', 'string'],
            'style_config.btn_text_secondary' => ['bail', 'nullable', 'string'],
            'style_config.btn_bg_primary' => ['bail', 'nullable', 'string'],
            'style_config.btn_bg_secondary' => ['bail', 'nullable', 'string'],
            'multimedia_config' => ['bail', 'required'],
            'multimedia_config.logo' => ['bail', 'nullable'],
            'multimedia_config.bg_video' => ['bail', 'nullable'],
            'multimedia_config.monitor_bg_video' => ['bail', 'nullable'],
            'black_list' => ['bail', 'nullable', 'array'],
            'participants_excel' => ['bail', 'nullable'],
        ];
    }
}
