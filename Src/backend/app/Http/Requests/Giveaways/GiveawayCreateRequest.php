<?php

namespace App\Http\Requests\Giveaways;

use App\Core\Bases\BaseRequest;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use Illuminate\Validation\Rule;

class GiveawayCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $giveawayId = null, ?array $params = []): array
    {
        return [
            'name' => ['bail', 'required', 'string'],
            'quiz' => ['bail', 'required'],
            'quiz.id' => ['bail', 'required', 'exists:quizzes,id'],
            'allowed_participants_mode' => ['bail', 'required', Rule::in(GiveawayAllowedParticipantsModeEnum::names())],
            'min_correct_answers' => ['bail', 'nullable', 'numeric'],
            'logo' => ['bail', 'nullable'],
            'bg_video' => ['bail', 'nullable'],
            'monitor_bg_video' => ['bail', 'nullable'],
            'color_primary' => ['bail', 'nullable', 'string'],
            'audio_enabled' => ['bail', 'nullable', 'boolean'],
            'draw_duration_seconds' => ['bail', 'nullable', 'numeric'],
            'prizes' => ['bail', 'required', 'array'],
            'prizes.*.id' => ['bail', 'nullable'],
            'prizes.*.name' => ['bail', 'required', 'string'],
            'prizes.*.description' => ['bail', 'nullable', 'string'],
            'prizes.*.quantity' => ['bail', 'required', 'numeric'],
            'prizes.*.min_position' => ['bail', 'nullable', 'numeric'],
            'prizes.*.max_position' => ['bail', 'nullable', 'numeric'],
            'prizes.*.image' => ['bail', 'nullable'],
        ];
    }
}
