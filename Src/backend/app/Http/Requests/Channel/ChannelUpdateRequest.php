<?php

namespace App\Http\Requests\Channel;

use App\Core\Bases\BaseRequest;

class ChannelUpdateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(int $channelId, ?array $params = []): array
    {
        $rules = ChannelCreateRequest::rules($channelId);

        return $rules;
    }
}
