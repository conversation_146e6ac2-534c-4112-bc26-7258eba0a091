<?php

namespace App\Http\Requests\Channel;

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use App\Enums\ChannelQuizConfigScoringModeEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Core\Bases\BaseRequest;
use App\Models\Channel;
use App\Models\SystemUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rule;

class ChannelCreateRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public static function rules(?int $channelId = null, ?array $params = []): array
    {
        $slugUniqueRule = Rule::unique('channels', 'slug')
            ->when($channelId, function ($query) use ($channelId) {
                return $query->ignore($channelId);
            });

        return [
            'name' => ['bail', 'required', 'string'],
            'slug' => ['bail', 'required', 'string', $slugUniqueRule],
            'system_user' => ['bail', 'nullable'],
            'system_user.id' => ['bail', 'nullable', 'exists:App\Models\SystemUser,id'],
            'website_url' => ['bail', 'nullable', 'string'],
            'tyc_url' => ['bail', 'nullable', 'string'],
            'email' => ['bail', 'nullable', 'string'],
            'config' => ['bail', 'required'],
            'config.max_users_per_quiz' => ['bail', 'required', 'numeric'],
            'config.time_per_question' => ['bail', 'required', 'numeric'],
            'config.message_primary' => ['bail', 'nullable', 'string'],
            'config.message_secondary' => ['bail', 'nullable', 'string'],
            'config.allow_lose_points' => ['bail', 'required', 'boolean'],
            'config.allow_negative_points' => ['bail', 'required', 'boolean'],
            'config.use_random_questions' => ['bail', 'required', 'boolean'],
            'config.show_ranking_between_questions' => ['bail', 'required', 'boolean'],
            'config.time_per_ranking' => ['bail', 'nullable', 'numeric'],
            'config.show_correct_answer' => ['bail', 'required', 'boolean'],
            'config.play_audio_on_monitor' => ['bail', 'required', 'boolean'],
            'config.ask_for_email_at_the_end' => ['bail', 'required', Rule::in(ChannelQuizConfigAskForEmailEnum::names())],
            'config.scoring_mode' => ['bail', 'required', Rule::in(ChannelQuizConfigScoringModeEnum::names())],
            'config.show_ranking_upon_completion' => ['bail', 'nullable', 'boolean'],
            'config.only_correct_winning_message' => ['bail', 'nullable', 'string'],
            'config.only_correct_loser_message' => ['bail', 'nullable', 'string'],
            'config.minimum_correct_answers' => ['bail', 'required', 'numeric'],
            'config.multimedia_config' => ['bail', 'required'],
            'config.multimedia_config.logo' => ['bail', 'nullable'],
            'config.multimedia_config.logo_link' => ['bail', 'nullable', 'url'],
            'config.multimedia_config.bg_video' => ['bail', 'nullable'],
            'config.multimedia_config.bg_image' => ['bail', 'nullable'],
            'config.multimedia_config.monitor_bg_video' => ['bail', 'nullable'],
            'config.multimedia_config.monitor_bg_image' => ['bail', 'nullable'],
            'config.multimedia_config.banner' => ['bail', 'nullable'],
            'config.multimedia_config.banner_link' => ['bail', 'nullable', 'url'],
            'config.style_config' => ['bail', 'required'],
            'config.style_config.css_file' => ['bail', 'nullable'],
            'config.style_config.primary' => ['bail', 'nullable', 'string'],
            'config.style_config.secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.text_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.text_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.bg_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.bg_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_text_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_text_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_bg_primary' => ['bail', 'nullable', 'string'],
            'config.style_config.btn_bg_secondary' => ['bail', 'nullable', 'string'],
            'config.style_config.register_label_text' => ['bail', 'nullable', 'string'],
            'config.style_config.register_input_text' => ['bail', 'nullable', 'string'],
            'config.style_config.progress_bar_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.question_text' => ['bail', 'nullable', 'string'],
            'config.style_config.question_text_border' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_text' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_selected' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_correct' => ['bail', 'nullable', 'string'],
            'config.style_config.answer_btn_bg_incorrect' => ['bail', 'nullable', 'string'],
            'config.style_config.ranking_text' => ['bail', 'nullable', 'string'],
            'config.style_config.ranking_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.banner_btn_bg' => ['bail', 'nullable', 'string'],
            'config.style_config.banner_btn_text' => ['bail', 'nullable', 'string'],
            'config.register_config_fields' => ['bail', 'nullable', 'array'],
            'config.register_config_fields.*.id' => ['bail', 'nullable'],
            'config.register_config_fields.*.label' => ['bail', 'required', 'string'],
            'config.register_config_fields.*.type' => ['bail', 'required', Rule::in(RegisterConfigFieldTypeEnum::names())],
            'config.register_config_fields.*.validations' => ['bail', 'nullable'],
            'config.register_config_fields.*.show_in_ranking' => ['bail', 'nullable', 'boolean'],
            'config.register_config_fields.*.label_for_ranking' => ['bail', 'nullable', 'string'],
        ];
    }
}
