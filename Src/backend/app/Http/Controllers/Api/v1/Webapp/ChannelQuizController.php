<?php

namespace App\Http\Controllers\Api\v1\Webapp;

use App\DynamoDbModels\SyncedQuiz;
use App\Enums\ErrorEnum;
use App\Core\Response\Response;
use App\Enums\ChannelStatusEnum;
use App\Enums\QuizStatusEnum;
use App\Enums\QuizTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\Channel;
use App\Models\Quiz;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ChannelQuizController extends Controller
{
    public function index(int $channelId)
    {
        $channel = Channel::findOrFail($channelId);

        if ($channel->status->is(ChannelStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_CHANNEL_ERROR->throw();
        }

        $quizzes = $channel->quizzes()
            ->active()
            ->with('question_set')
            ->orderBy('starts_at')
            ->paginate()
            ;

        $quizzes->each(function ($quiz) {
            $quiz->makeHidden(['monitor_pin', 'pin']);
        });

        $nextQuiz = $quizzes->filter(function ($quiz) {
            return !($quiz->type->is(QuizTypeEnum::OnDemand) && Carbon::parse($quiz->starts_at)->isPast());
        })->sortBy('starts_at')->first();

        return Response::json([
            'quizzes' => $quizzes,
            'next_quiz_id' => $nextQuiz ? $nextQuiz->id : null,
        ]);
    }

    public function show(int $channelId, int $quizId)
    {
        $quiz = SyncedQuiz::find($quizId);

        if (!$quiz) {
            ErrorEnum::NO_QUIZ_ERROR->throw();
        }

        $quiz->append(['itinerary', 'ranking', 'auth_user_profile', 'auth_user_progress']);

        if ($quiz->status === QuizStatusEnum::Inactive->name) {
            ErrorEnum::INACTIVE_QUIZ_ERROR->throw();
        }

        return Response::json([
            'quiz' => $quiz,
        ]);
    }

    /* -------------------- */

    public function play(int $channelId, int $quizId)
    {
        $channel = Channel::findOrFail($channelId);

        if ($channel->status->is(ChannelStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_CHANNEL_ERROR->throw();
        }

        $quiz = Quiz::findOrFail($quizId);

        if ($quiz->status->is(QuizStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_QUIZ_ERROR->throw();
        }

        DB::beginTransaction();

        $quiz->play();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido reanudada con éxito.');
    }

    public function pause(int $channelId, int $quizId)
    {
        $channel = Channel::findOrFail($channelId);

        if ($channel->status->is(ChannelStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_CHANNEL_ERROR->throw();
        }

        $quiz = Quiz::findOrFail($quizId);

        if ($quiz->status->is(QuizStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_QUIZ_ERROR->throw();
        }

        DB::beginTransaction();

        $quiz->pause();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido pausada con éxito.');
    }
}
