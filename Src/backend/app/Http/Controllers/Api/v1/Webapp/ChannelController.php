<?php

namespace App\Http\Controllers\Api\v1\Webapp;

use App\Core\Response\Response;
use App\Http\Controllers\Controller;
use App\Models\Channel;
use Illuminate\Support\Facades\Cache;

class ChannelController extends Controller
{
    public function getBySlug(string $channelSlug)
    {
        $channel = Channel::with('quizzes')
            ->whereSlug($channelSlug)
            ->withConfigFull()
            ->firstOrFail();

        $channel->quizzes->each(function ($quiz) {
            $quiz->makeHidden(['monitor_pin', 'pin']);
        });

        // $channel = Cache::remember("Webapp::ChannelController::getBySlug::$channelSlug", 600, function () use ($channelSlug) {
        //     return Channel::with('quizzes')
        //         ->whereSlug($channelSlug)
        //         ->withConfigFull()
        //         ->firstOrFail();
        // });

        return Response::json([
            'channel' => $channel,
        ]);
    }
}
