<?php

namespace App\Http\Controllers\Api\v1\Webapp;

use App\DynamoDbModels\SyncedQuiz;
use App\Facades\Auth;
use App\Enums\ErrorEnum;
use App\Core\Response\Response;
use App\DynamoDbModels\UserQuizProgress;
use App\Enums\QuizTypeEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Quiz\QuizUserRegisterProfileRequest;
use App\Http\Requests\Quiz\QuizUserAccessRequest;
use App\Http\Requests\Quiz\QuizUserProgressUpdateRequest;
use App\Http\Requests\Quiz\QuizUserSetEndEmailRequest;
use App\Http\Requests\Quiz\QuizUserUpdateCurrentStepIdProfileRequest;
use App\Models\Channel;
use App\Models\UserQuiz;
use App\Repositories\UserQuizProfileRepository;
use App\Repositories\UserQuizProgressRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ChannelQuizUserController extends Controller
{
    public function access(int $channelId, int $quizId)
    {
        $channel = Channel::findOrFail($channelId);
        $quiz = $channel->quizzes()->findOrFail($quizId);

        $input = request()->post();
        $validated = Validator::make($input, QuizUserAccessRequest::rules())->validate();

        DB::beginTransaction();

        if (!Auth::user()->quizzes()->where('quizzes.id', $quizId)->exists()) {
            if ($quiz->pin !== $validated['pin']) {
                ErrorEnum::QUIZ_ACCESS_PIN_ERROR->throw();
            }

            Auth::user()->quizzes()->attach($quiz);
        }

        DB::commit();

        return Response::json([
            'can_access' => true
        ]);
    }

    public function registerProfile(int $channelId, int $quizId)
    {
        $quiz = SyncedQuiz::findOrFail($quizId);

        if ($quiz->auth_user_profile) {
            ErrorEnum::ALREADY_USER_QUIZ_PROFILE_ERROR->throw();
        }

        // if (!$quiz->is_public && !UserQuiz::whereQuizId($quiz->id)->whereUserId(Auth::user()->id)->exists()) {
        //     ErrorEnum::QUIZ_ACCESS_ERROR->throw();
        // }

        if (!$quiz->has_room) {
            ErrorEnum::QUIZ_MAX_USERS_LIMIT_REACHED_ERROR->throw();
        }

        $input = request()->post();

        foreach ($quiz->config['register_config_fields'] as $field) {
            if ($field['type'] === RegisterConfigFieldTypeEnum::DefaultEmail->name) {
                $input[$field['name']] = strtolower($input[$field['name']]);
            }
        }

        $validated = Validator::make($input,
            QuizUserRegisterProfileRequest::rules($quizId),
        )->validate();

        // DB::beginTransaction();

        $validated['quiz_id'] = $quizId;
        $validated['quiz_name'] = $quiz->name;
        $validated['user_id'] = Auth::user()->id;
        $validated['channel_id'] = $quiz->channel['id'];
        $validated['channel_name'] = $quiz->channel['name'];

        if ($quiz->type === QuizTypeEnum::OnDemand->name && $quiz->config['use_random_questions']) {
            $validated['question_ids'] = $quiz->getRandomQuestionIds();
        }

        $validated['register_field_values'] = [];

        foreach ($quiz->config['register_config_fields'] as $field) {
            if ($validated[$field['name']]) {
                $validated['register_field_values'][] = [
                    'value' => $validated[$field['name']],
                    'field' => $field,
                ];

                switch ($field['type']) {
                    case RegisterConfigFieldTypeEnum::DefaultName->name: {
                        $validated['name'] = $validated[$field['name']];
                        break;
                    }

                    case RegisterConfigFieldTypeEnum::DefaultEmail->name: {
                        $validated['email'] = $validated[$field['name']];
                        break;
                    }

                    case RegisterConfigFieldTypeEnum::DefaultDNI->name: {
                        $validated['dni'] = $validated[$field['name']];
                        break;
                    }
                }
            }
        }

        if (count($quiz->groups)) {
            $validated = $quiz->resolveGroup($validated);
        }

        $validated['id'] = "quizId:{$validated['quiz_id']}_" . "userId:{$validated['user_id']}";

        $userQuizProfile = UserQuizProfileRepository::save($validated);

        // DB::commit();

        return Response::json([
            'user_profile_name' => $userQuizProfile->name,
            'quiz_group_name' => $userQuizProfile->group->name ?? null,
        ], 'El perfil ha sido registrado con éxito.');
    }

    public function updateCurrentStepIdProfile(int $channelId, int $quizId)
    {
        $quiz = SyncedQuiz::findOrFail($quizId);
        $userProfile = $quiz->auth_user_profile;

        if (!$userProfile) {
            ErrorEnum::NO_USER_QUIZ_PROFILE_ERROR->throw();
        }

        // if (!$quiz->is_public && !UserQuiz::whereQuizId($quiz->id)->whereUserId(Auth::user()->id)->exists()) {
        //     ErrorEnum::QUIZ_ACCESS_ERROR->throw();
        // }

        $input = request()->post();
        $validated = Validator::make($input, QuizUserUpdateCurrentStepIdProfileRequest::rules())->validate();

        // DB::beginTransaction();

        $userProfile->current_step_id = $validated['current_step_id'];
        $userProfile->saveOrFail();

        // DB::commit();

        return Response::json([
            'profile' => $userProfile
        ], 'El step actual del perfil ha sido actualizado con éxito.');
    }

    public function setProgress(int $channelId, int $quizId)
    {
        $quiz = SyncedQuiz::findOrFail($quizId);

        // if (!$quiz->is_public && !UserQuiz::whereQuizId($quiz->id)->whereUserId(Auth::user()->id)->exists()) {
        //     ErrorEnum::QUIZ_ACCESS_ERROR->throw();
        // }

        $input = request()->post();
        $validated = Validator::make($input, QuizUserProgressUpdateRequest::rules())->validate();
        $validated['id'] = "quizId:{$quizId}_" . "userId:" . Auth::user()->id . "_stepId:{$validated['step_id']}";

        if (UserQuizProgress::find($validated['id'])) {
            ErrorEnum::ALREADY_USER_QUIZ_PROGRESS_ERROR->throw();
        }

        // DB::beginTransaction();

        $validated['quiz_id'] = $quizId;
        $validated['user_id'] = Auth::user()->id;
        $validated['quiz_type'] = $quiz->type;
        $validated['question_id'] = $validated['question']['id'];
        $validated['points_multiplier'] = $validated['question']['typeEnum']['multiplier'];
        $validated['selected_answer_id'] = $validated['selected_answer']['id'];
        $validated['selected_answer_is_correct'] = $validated['selected_answer']['isCorrect'];

        $progress = UserQuizProgressRepository::save($validated);

        // DB::commit();

        return Response::json([
            'progress' => $progress,
        ], 'El progreso ha sido actualizado con éxito.');
    }

    public function setEndEmail(int $channelId, int $quizId)
    {
        $quiz = SyncedQuiz::findOrFail($quizId);
        $userProfile = $quiz->auth_user_profile;

        if (!$userProfile) {
            ErrorEnum::NO_USER_QUIZ_PROFILE_ERROR->throw();
        }

        // if (!$quiz->is_public && !UserQuiz::whereQuizId($quiz->id)->whereUserId(Auth::user()->id)->exists()) {
        //     ErrorEnum::QUIZ_ACCESS_ERROR->throw();
        // }

        $input = request()->post();
        $validated = Validator::make($input, QuizUserSetEndEmailRequest::rules())->validate();

        // DB::beginTransaction();

        $userProfile->end_email = $validated['end_email'];
        $userProfile->saveOrFail();

        // DB::commit();

        return Response::json([
            'profile' => $userProfile,
        ], 'El email ha sido guardado con éxito.');
    }
}
