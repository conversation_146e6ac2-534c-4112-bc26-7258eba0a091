<?php

namespace App\Http\Controllers\Api\v1\Webapp;

use App\Enums\ErrorEnum;
use App\Core\Response\Response;
use App\Http\Controllers\Controller;
use App\Models\Giveaway;

class GiveawayController extends Controller
{
    public function show(int $giveawayId)
    {
        $giveaway = Giveaway::find($giveawayId);

        if (!$giveaway) {
            ErrorEnum::NO_QUIZ_ERROR->throw();
        }

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }
}
