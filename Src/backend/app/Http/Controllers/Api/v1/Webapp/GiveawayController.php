<?php

namespace App\Http\Controllers\Api\v1\Webapp;

use App\Enums\ErrorEnum;
use App\Core\Response\Response;
use App\Enums\GiveawayStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Giveaway;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class GiveawayController extends Controller
{
    public function show(int $giveawayId)
    {
        $giveaway = Giveaway::find($giveawayId);

        if (!$giveaway) {
            ErrorEnum::NO_GIVEAWAY_ERROR->throw();
        }

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }

    public function start(int $giveawayId)
    {
        $giveaway = Giveaway::find($giveawayId);

        if (!$giveaway) {
            ErrorEnum::NO_GIVEAWAY_ERROR->throw();
        }

        DB::beginTransaction();

        $giveaway->status = GiveawayStatusEnum::InProgress;
        $giveaway->saveOrFail();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }

    public function revealWinner(int $giveawayId)
    {
        $giveaway = Giveaway::find($giveawayId);

        if (!$giveaway) {
            ErrorEnum::NO_GIVEAWAY_ERROR->throw();
        }

        $input = request()->post();
        $validated = Validator::make($input, [
            'prize_id' => 'bail|required|exists:giveaway_prizes,id',
            'reveal_all' => 'bail|required|boolean',
            'winner_id' => 'bail|nullable|required_if:reveal_all,false',
        ])->validate();

        $winners = $giveaway->winners;

        foreach ($winners as $key => $winner) {
            if ($winner['prize_id'] !== $validated['prize_id']) {
                continue;
            }

            if ($validated['reveal_all']) {
                $winners[$key]['revealed'] = true;
                continue;
            }

            if ($validated['winner_id'] && $winner['id'] === $validated['winner_id']) {
                $winners[$key]['revealed'] = true;
                break;
            }
        }

        DB::beginTransaction();

        $giveaway->winners = $winners;

        if ($giveaway->allWinnersRevealed()) {
            $giveaway->status = GiveawayStatusEnum::Completed;
        }

        $giveaway->saveOrFail();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }
}
