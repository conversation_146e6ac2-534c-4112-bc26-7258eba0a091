<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\Core\RESTful\RESTful;
use App\Enums\ErrorEnum;
use App\Enums\QuizStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Quiz\QuizCreateRequest;
use App\Http\Requests\Quiz\QuizUpdateRequest;
use App\Models\Quiz;
use App\Repositories\QuizRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class QuizController extends Controller
{
    public function index()
    {
        $quizzes = (new RESTful(
            Quiz::query(),
            request()->query(),
        ))->paginate();

        return Response::json([
            'quizzes' => $quizzes,
        ]);
    }

    public function show(int $quizId)
    {
        $quiz = (new RESTful(
            Quiz::query(),
            request()->query(),
        ))->findOrFail($quizId);

        return Response::json([
            'quiz' => $quiz,
        ]);
    }

    public function create()
    {
        $input = request()->post();
        $validated = Validator::make($input, QuizCreateRequest::rules())->validate();

        DB::beginTransaction();

        $quiz = QuizRepository::save($validated);

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida ha sido creada con éxito.');
    }

    public function update(int $quizId)
    {
        $quiz = Quiz::findOrFail($quizId);

        $input = request()->post();
        $validated = Validator::make($input, QuizUpdateRequest::rules($quiz->id))->validate();

        DB::beginTransaction();

        $quiz = QuizRepository::save($validated, $quiz);

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido actualizada con éxito.');
    }

    public function delete(int $quizId)
    {
        $quiz = Quiz::findOrFail($quizId);

        DB::beginTransaction();

        $quiz->delete();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido eliminada con éxito.');
    }

    /* -------------------- */

    public function activate(int $quizId)
    {
        $quiz = Quiz::with('channel')->findOrFail($quizId);

        DB::beginTransaction();

        $quiz->status = QuizStatusEnum::Active;
        $quiz->saveOrFail();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido activada con éxito.');
    }

    public function deactivate(int $quizId)
    {
        $quiz = Quiz::with('channel')->findOrFail($quizId);

        DB::beginTransaction();

        $quiz->status = QuizStatusEnum::Inactive;
        $quiz->saveOrFail();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido desactivada con éxito.');
    }

    /* -------------------- */

    public function play(int $quizId)
    {
        $quiz = Quiz::findOrFail($quizId);

        if ($quiz->status->is(QuizStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_QUIZ_ERROR->throw();
        }

        DB::beginTransaction();

        $quiz->play();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido reanudada con éxito.');
    }

    public function pause(int $quizId)
    {
        $quiz = Quiz::findOrFail($quizId);

        if ($quiz->status->is(QuizStatusEnum::Inactive)) {
            ErrorEnum::INACTIVE_QUIZ_ERROR->throw();
        }

        DB::beginTransaction();

        $quiz->pause();

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida provista ha sido pausada con éxito.');
    }

    /* -------------------- */

    public function duplicate(int $quizId)
    {
        DB::beginTransaction();

        $quiz = Quiz::duplicate($quizId);

        DB::commit();

        return Response::json([
            'quiz' => $quiz,
        ], 'La partida ha sido duplicada con éxito.');
    }
}
