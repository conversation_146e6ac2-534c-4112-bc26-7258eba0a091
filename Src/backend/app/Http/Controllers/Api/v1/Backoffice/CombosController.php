<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Facades\Auth;
use App\Core\Response\Response;
use App\DynamoDbModels\SyncedQuiz;
use App\Enums\ChannelStatusEnum;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Enums\GiveawayStatusEnum;
use App\Enums\QuestionSetStatusEnum;
use App\Enums\QuestionTypeEnum;
use App\Enums\QuizModeEnum;
use App\Enums\QuizStatusEnum;
use App\Enums\QuizTypeEnum;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Enums\SystemUserStatusEnum;
use App\Enums\SystemUserTypeEnum;
use App\Enums\WhitelistStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Channel;
use App\Models\QuestionSet;
use App\Models\Quiz;
use App\Models\SystemUser;
use App\Models\ThemeTag;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CombosController extends Controller
{
    public function get()
    {
        $concepts = explode(',', request()->query('concepts'));
        $combos = [];

        foreach ($concepts as $concept) {
            $conceptExploded = explode(':', $concept);
            $conceptParam = $conceptExploded[1] ?? null;
            $concept = $conceptExploded[0];

            switch ($concept) {
                case 'system_user_roles': {
                    $combos[$concept] = Role::whereGuardName(Auth::getSystemUserGuardName())->get()->map(fn ($item) => $item['name']);
                    break;
                }

                case 'system_user_permissions': {
                    $combos[$concept] = Permission::whereGuardName(Auth::getSystemUserGuardName())->get()->map(fn ($item) => $item['name']);
                    break;
                }

                case 'system_user_types': {
                    $combos[$concept] = collect(SystemUserTypeEnum::all())->filter(function ($value) {
                        return $value['name'] !== SystemUserTypeEnum::ClientChild->name;
                    })->toArray();

                    break;
                }

                case 'system_user_statuses': {
                    $combos[$concept] = SystemUserStatusEnum::all();
                    break;
                }

                case 'system_user_clients': {
                    $combos[$concept] = SystemUser::whereType(SystemUserTypeEnum::Client->name)->get();
                    break;
                }

                case 'system_user_clients_for_channel_assignment': {
                    $combos[$concept] = SystemUser::whereType(SystemUserTypeEnum::Client->name)->get();

                    break;
                }

                /* -------------------- */

                case 'channel_statuses': {
                    $combos[$concept] = ChannelStatusEnum::all();
                    break;
                }

                case 'channels': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Channel::all()->sortByDesc("id")->values();

                    break;
                }

                case 'channels_for_system_user_client_assignment': {
                    $combos[$concept] = Channel::doesntHave('system_user')
                        ->when($conceptParam, function ($query) use ($conceptParam) {
                            return $query->orWhere('system_user_id', $conceptParam);
                        })->get()
                        ->sortByDesc('id')->values();

                    break;
                }

                case 'channels_for_copy_or_quiz_assignment': {
                    if (!Auth::systemUser()) break;

                    $channels = Channel::withConfigFull()->get()->sortByDesc('id')->values();

                    foreach ($channels as $channel) {
                        $channel->config->makeHidden(['id', 'multimedia_config_id', 'style_config_id']);
                        $channel->config->multimedia_config->makeHidden(['id']);
                        $channel->config->style_config->makeHidden(['id']);
                        $channel->config->prize_configs->makeHidden(['id', 'config_id']);
                        $channel->config->register_config_fields->makeHidden(['id', 'config_id']);
                    }

                    $combos[$concept] = $channels;

                    break;
                }

                case 'channels_with_quizzes_with_questions_sets': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Channel::whereHas('quizzes', function ($query) {
                        $query->has('question_set');
                        })->get()
                        ->sortByDesc('id')->values();

                    break;
                }

                /* -------------------- */

                case 'questions_sets': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = QuestionSet::all()->sortByDesc('id')->values();

                    break;
                }

                case 'question_set_statuses': {
                    $combos[$concept] = QuestionSetStatusEnum::all();
                    break;
                }

                case 'theme_tags': {
                    $combos[$concept] = ThemeTag::all();
                    break;
                }

                case 'question_types': {
                    $combos[$concept] = QuestionTypeEnum::all();
                    break;
                }

                /* -------------------- */

                case 'quizzes': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Quiz::all()->sortByDesc('id')->values();;

                    break;
                }

                case 'quiz_statuses': {
                    $combos[$concept] = QuizStatusEnum::all();
                    break;
                }

                case 'quiz_types': {
                    $combos[$concept] = QuizTypeEnum::all();
                    break;
                }

                case 'quiz_modes': {
                    $combos[$concept] = QuizModeEnum::all();
                    break;
                }

                /* -------------------- */

                case 'register_config_field_types': {
                    $combos[$concept] = collect(RegisterConfigFieldTypeEnum::all())->filter(fn ($item) => !Str::startsWith($item['name'], 'Default'))->values()->toArray();
                    break;
                }

                case 'register_config_field_type_defaults': {
                    $combos[$concept] = collect(RegisterConfigFieldTypeEnum::all())->filter(fn ($item) => Str::startsWith($item['name'], 'Default'))->values()->toArray();
                    break;
                }

                /* -------------------- */

                case 'whitelist_statuses': {
                    $combos[$concept] = WhitelistStatusEnum::all();
                    break;
                }

                case 'whitelist_field_names': {
                    $combos[$concept] = collect(RegisterConfigFieldTypeEnum::all())->filter(fn ($item) => Str::startsWith($item['name'], 'Default') && $item['name'] !== 'DefaultName')->values()->toArray();
                    break;
                }

                /* -------------------- */

                case 'channels_for_reports': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Channel::select('id', 'name', 'slug')->get()->sortByDesc('id')->values();

                    break;
                }

                case 'quizzes_for_reports': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = SyncedQuiz::all()->sortByDesc('id')->values();

                    break;
                }

                /* -------------------- */

                case 'channels_for_giveaways': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Channel::select('id', 'name', 'slug')->get()->sortByDesc('id')->values();

                    break;
                }

                case 'quizzes_for_giveaways': {
                    if (!Auth::systemUser()) break;

                    $combos[$concept] = Quiz::all()->sortByDesc('id')->values();

                    break;
                }

                case 'giveaway_allowed_participants_modes': {
                    $combos[$concept] = GiveawayAllowedParticipantsModeEnum::all();
                    break;
                }

                case 'giveaway_statuses': {
                    $combos[$concept] = GiveawayStatusEnum::all();
                    break;
                }
            }
        }

        return Response::json([
            'combos' => $combos,
        ]);
    }
}
