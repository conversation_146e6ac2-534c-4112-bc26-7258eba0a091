<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\DynamoDbModels\QuizRanking;
use App\DynamoDbModels\UserQuizProfile;
use App\Http\Controllers\Controller;
use App\Models\Quiz;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class UserQuizProfileController extends Controller
{
    public function index()
    {
        $userQuizProfiles = collect();
        $updatedAtCollection = collect();

        Quiz::chunkById(200, function ($quizzes) use (&$userQuizProfiles, &$updatedAtCollection) {
            foreach ($quizzes as $quiz) {
                $cache = Cache::get("UserQuizProfiles:$quiz->id");

                if (!empty($cache['data'])) {
                    $userQuizProfiles->push($cache['data']);
                    $updatedAtCollection->push($cache['updated_at']);
                }
            }
        });

        return Response::json([
            'user_quiz_profiles' => [
                'data' => $userQuizProfiles->flatten()->values() ?? [],
                'updated_at' => $updatedAtCollection->max() ?? null,
            ],
        ]);
    }

    public function show(string $userQuizProfileId)
    {
        $userQuizProfile = UserQuizProfile::findOrFail($userQuizProfileId);
        $userQuizProfile->append(['group', 'last_progress']);
        $userQuizProfile['quiz_ranking'] = QuizRanking::where('quiz_id', $userQuizProfile->quiz_id)->first();

        return Response::json([
            'user_quiz_profile' => $userQuizProfile,
        ]);
    }

    public function delete(string $userQuizProfileId)
    {
        $userQuizProfile = UserQuizProfile::findOrFail($userQuizProfileId);

        // DB::beginTransaction();

        $userQuizProfile->delete();

        // DB::commit();

        return Response::json([
            'user_quiz_profile' => $userQuizProfile,
        ], 'El perfil de usuario provisto ha sido eliminado con éxito.');
    }
}
