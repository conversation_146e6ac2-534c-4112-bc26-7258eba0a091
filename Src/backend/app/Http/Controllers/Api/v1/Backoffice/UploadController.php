<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\Http\Controllers\Controller;
use App\Models\MediaTmp;

class UploadController extends Controller
{
    public function upload()
    {
        sleep(1);

        $concept = request()->query('concept');
        $mediaTmp = MediaTmp::firstOrCreate();

        $file = $mediaTmp->addMediaFromRequest('file')
            ->toMediaCollection('tmp_' . $concept);

        return Response::json([
            'file' => $file,
        ]);
    }
}
