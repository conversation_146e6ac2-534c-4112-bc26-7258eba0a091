<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Facades\Auth;
use App\Core\Response\Response;
use App\Core\RESTful\RESTful;
use App\Enums\QuestionSetStatusEnum;
use App\Enums\QuestionTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\QuestionSet\QuestionSetCreateRequest;
use App\Http\Requests\QuestionSet\QuestionSetUpdateRequest;
use App\Imports\QuestionImport;
use App\Models\QuestionSet;
use App\Repositories\QuestionSetRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class QuestionSetController extends Controller
{
    public function index()
    {
        $questionsSets = (new RESTful(
            QuestionSet::query(),
            request()->query(),
        ))->paginate();

        return Response::json([
            'questions_sets' => $questionsSets,
        ]);
    }

    public function show(int $questionSetId)
    {
        $questionSet = (new RESTful(
            QuestionSet::query(),
            request()->query(),
        ))->findOrFail($questionSetId);

        return Response::json([
            'question_set' => $questionSet,
        ]);
    }

    public function create()
    {
        $input = request()->post();

        if ($input['use_import_file'] && !empty($input['import_file'])) {
            $input['questions'] = (new QuestionImport($input['import_file']['id'], true))->getData();
            $input['use_import_file'] = false;
            $input['import_file'] = null;
        }

        $validated = Validator::make($input, QuestionSetCreateRequest::rules())->validate();
        $validated['owner'] = Auth::systemUser();

        DB::beginTransaction();

        $questionSet = QuestionSetRepository::save($validated);

        DB::commit();

        return Response::json([
            'question_set' => $questionSet,
        ], 'El set de preguntas ha sido creado con éxito.');

    }
    public function update(int $questionSetId)
    {
        $questionSet = QuestionSet::findOrFail($questionSetId);

        $input = request()->post();
        $validated = Validator::make($input, QuestionSetUpdateRequest::rules($questionSet->id))->validate();

        DB::beginTransaction();

        $questionSet = QuestionSetRepository::save($validated, $questionSet);

        DB::commit();

        return Response::json([
            'question_set' => $questionSet,
        ], 'El set de preguntas provisto ha sido actualizado con éxito.');
    }

    public function delete(int $questionSetId)
    {
        $questionSet = QuestionSet::findOrFail($questionSetId);

        DB::beginTransaction();

        $questionSet->delete();

        DB::commit();

        return Response::json([
            'question_set' => $questionSet,
        ], 'El set de preguntas provisto ha sido eliminado con éxito.');
    }

    /* -------------------- */

    public function activate(int $questionSetId)
    {
        $questionSet = QuestionSet::with('questions')->withCount('quizzes')->findOrFail($questionSetId);

        DB::beginTransaction();

        $questionSet->status = QuestionSetStatusEnum::Active;
        $questionSet->saveOrFail();

        DB::commit();

        return Response::json([
            'question_set' => $questionSet,
        ], 'El set de preguntas provisto ha sido activado con éxito.');
    }

    public function deactivate(int $questionSetId)
    {
        $questionSet = QuestionSet::with('questions')->withCount('quizzes')->findOrFail($questionSetId);

        DB::beginTransaction();

        $questionSet->status = QuestionSetStatusEnum::Inactive;
        $questionSet->saveOrFail();

        DB::commit();

        return Response::json([
            'question_set' => $questionSet,
        ], 'El set de preguntas provisto ha sido desactivado con éxito.');
    }

    public function duplicate(int $questionSetId)
    {
        $questionSet = QuestionSet::with('questions')->findOrFail($questionSetId);

        $input = $questionSet->makeHidden(['id'])->toArray();
        $input['name'] = $input['name'] . '_copia';
        $duplicatedQuestions = [];
        foreach ( $questionSet->questions as $question) {
            $duplicatedQuestion = $question->makeHidden(['id', 'created_at', 'updated_at'])->toArray();
            $duplicatedQuestion ['pivot'] = $question->pivot->makeHidden(['question_id', 'set_id', 'created_at', 'updated_at'])->toArray();
            $duplicatedQuestion ['answers'] = $question->answers->makeHidden(['question_id', 'id', 'created_at', 'updated_at'])->toArray();;
            array_push($duplicatedQuestions, $duplicatedQuestion);
        }
        $input['questions'] = $duplicatedQuestions;

        DB::beginTransaction();

        $duplicatedQuestionSet = QuestionSetRepository::save($input);

        DB::commit();

        return Response::json([
            'question_set' => $duplicatedQuestionSet,
        ], 'El set de preguntas provisto ha sido duplicado con éxito.');
    }
}
