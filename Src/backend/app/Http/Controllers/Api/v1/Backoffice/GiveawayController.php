<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\Core\RESTful\RESTful;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Giveaways\GiveawayCreateRequest;
use App\Http\Requests\Giveaways\GiveawayResolveParticipantsRequest;
use App\Models\Giveaway;
use App\Repositories\GiveawayRepository;
use App\UseCases\Giveaway\ResolveParticipants\ResolveParticipants;
use App\UseCases\Giveaway\ResolveParticipants\ResolveParticipantsDTO;
use App\UseCases\Giveaway\ResolveParticipants\ResolveParticipantsProcess;
use App\UseCases\Giveaway\ResolveWinners\ResolveWinners;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class GiveawayController extends Controller
{
    public function __construct(
        private ResolveParticipants $resolveParticipants,
        private ResolveParticipantsProcess $resolveParticipantsProcess,
        private ResolveWinners $resolveWinners,
    ) {
    }

    public function index()
    {
        $giveaways = (new RESTful(
            Giveaway::query(),
            request()->query(),
        ))->paginate();

        return Response::json([
            'giveaways' => $giveaways,
        ]);
    }

    public function show(int $giveawayId)
    {
        $giveaway = (new RESTful(
            Giveaway::query(),
            request()->query(),
        ))->findOrFail($giveawayId);

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }

    public function create()
    {
        $input = request()->post();
        $validated = Validator::make($input, GiveawayCreateRequest::rules())->validate();

        DB::beginTransaction();

        $giveaway = GiveawayRepository::save($validated);

        $this->resolveParticipants->run($giveaway);
        $this->resolveWinners->run($giveaway);

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido creado con éxito.');
    }

    public function update(int $giveawayId)
    {
        $giveaway = Giveaway::findOrFail($giveawayId);

        $input = request()->post();
        $validated = Validator::make($input, GiveawayCreateRequest::rules($giveaway->id))->validate();

        DB::beginTransaction();

        $giveaway = GiveawayRepository::save($validated, $giveaway);

        $this->resolveParticipants->run($giveaway);
        $this->resolveWinners->run($giveaway);

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido actualizado con éxito.');
    }

    public function delete(int $giveawayId)
    {
        $giveaway = Giveaway::findOrFail($giveawayId);

        DB::beginTransaction();

        $giveaway->delete();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido eliminado con éxito.');
    }

    public function resolveParticipants()
    {
        $input = request()->post();
        $validated = Validator::make($input, GiveawayResolveParticipantsRequest::rules())->validate();

        $participants = $this->resolveParticipantsProcess->run(new ResolveParticipantsDTO(
            fileId: !$validated['quiz_id'] && $validated['file']
                ? $validated['file']['id']
                : null,
            quizId: $validated['quiz_id'],
            allowedParticipantsMode: $validated['allowed_participants_mode']
                ? GiveawayAllowedParticipantsModeEnum::tryFromName($validated['allowed_participants_mode'])
                : null,
            minCorrectAnswers: $validated['min_correct_answers'],
            maxTopRanking: $validated['max_top_ranking'],
        ));

        return Response::json([
            'participants' => $participants,
        ]);
    }
}
