<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\Core\RESTful\RESTful;
use App\Http\Controllers\Controller;
use App\Http\Requests\Giveaways\GiveawayCreateRequest;
use App\Models\Giveaway;
use App\Repositories\GiveawayRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class GiveawayController extends Controller
{
    public function index()
    {
        $giveaways = (new RESTful(
            Giveaway::query(),
            request()->query(),
        ))->paginate();

        return Response::json([
            'giveaways' => $giveaways,
        ]);
    }

    public function show(int $giveawayId)
    {
        $giveaway = (new RESTful(
            Giveaway::query(),
            request()->query(),
        ))->findOrFail($giveawayId);

        return Response::json([
            'giveaway' => $giveaway,
        ]);
    }

    public function create()
    {
        $input = request()->post();
        $validated = Validator::make($input, GiveawayCreateRequest::rules())->validate();

        DB::beginTransaction();

        $giveaway = GiveawayRepository::save($validated);

        $giveaway->resolveAndSaveParticipants();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido creado con éxito.');
    }

    public function update(int $giveawayId)
    {
        $giveaway = Giveaway::findOrFail($giveawayId);

        $input = request()->post();
        $validated = Validator::make($input, GiveawayCreateRequest::rules($giveaway->id))->validate();

        DB::beginTransaction();

        $giveaway = GiveawayRepository::save($validated, $giveaway);

        $giveaway->resolveAndSaveParticipants();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido actualizado con éxito.');
    }

    public function delete(int $giveawayId)
    {
        $giveaway = Giveaway::findOrFail($giveawayId);

        DB::beginTransaction();

        $giveaway->delete();

        DB::commit();

        return Response::json([
            'giveaway' => $giveaway,
        ], 'El sorteo ha sido eliminado con éxito.');
    }
}
