<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Facades\Auth;
use App\Core\Response\Response;
use App\Core\RESTful\RESTful;
use App\Enums\ChannelStatusEnum;
use App\Enums\SystemUserTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Channel\ChannelCreateRequest;
use App\Http\Requests\Channel\ChannelUpdateRequest;
use App\Models\Channel;
use App\Repositories\ChannelRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ChannelController extends Controller
{
    public function index()
    {
        $channels = (new RESTful(
            Channel::query(),
            request()->query(),
        ))->paginate();

        return Response::json([
            'channels' => $channels,
        ]);
    }

    public function show(int $channelId)
    {
        $channel = (new RESTful(
            Channel::query(),
            request()->query(),
        ))->findOrFail($channelId);

        return Response::json([
            'channel' => $channel,
        ]);
    }

    public function create()
    {
        $input = request()->post();
        $validated = Validator::make($input, ChannelCreateRequest::rules())->validate();

        if (!Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            $validated['system_user'] = Auth::systemUser();
        }

        DB::beginTransaction();

        $channel = ChannelRepository::save($validated);

        DB::commit();

        return Response::json([
            'channel' => $channel,
        ], 'El canal ha sido creado con éxito.');

    }
    public function update(int $channelId)
    {
        $channel = Channel::findOrFail($channelId);

        $input = request()->post();
        $validated = Validator::make($input, ChannelUpdateRequest::rules($channel->id))->validate();

        DB::beginTransaction();

        $channel = ChannelRepository::save($validated, $channel);

        DB::commit();

        return Response::json([
            'channel' => $channel,
        ], 'El canal provisto ha sido actualizado con éxito.');
    }

    public function delete(int $channelId)
    {
        $channel = Channel::findOrFail($channelId);

        DB::beginTransaction();

        $channel->delete();

        DB::commit();

        return Response::json([
            'channel' => $channel,
        ], 'El canal provisto ha sido eliminado con éxito.');
    }

    /* -------------------- */

    public function activate(int $channelId)
    {
        $channel = Channel::withConfigFull()->with('system_user')->withCount('quizzes')->findOrFail($channelId);

        DB::beginTransaction();

        $channel->status = ChannelStatusEnum::Active;
        $channel->saveOrFail();

        DB::commit();

        return Response::json([
            'channel' => $channel,
        ], 'El canal provisto ha sido activado con éxito.');
    }

    public function deactivate(int $channelId)
    {
        $channel = Channel::withConfigFull()->with('system_user')->withCount('quizzes')->findOrFail($channelId);

        DB::beginTransaction();

        $channel->status = ChannelStatusEnum::Inactive;
        $channel->saveOrFail();

        DB::commit();

        return Response::json([
            'channel' => $channel,
        ], 'El canal provisto ha sido desactivado con éxito.');
    }
}
