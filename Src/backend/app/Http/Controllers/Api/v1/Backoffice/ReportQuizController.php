<?php

namespace App\Http\Controllers\Api\v1\Backoffice;

use App\Core\Response\Response;
use App\DynamoDbModels\SyncedQuiz;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class ReportQuizController extends Controller
{
    public function get(string $quizIds)
    {
        $quizIds = array_map('intval', explode(',', $quizIds));
        $quizzes = collect(SyncedQuiz::findMany($quizIds));

        if ($quizzes->count() > 1) {
            $report = SyncedQuiz::getReports($quizIds);
        } else {
            $quiz = $quizzes->first();
            $report = Cache::get("QuizReport:$quiz->id", null);
        }

        return Response::json([
            'quiz_report' => $report,
        ]);
    }
}
