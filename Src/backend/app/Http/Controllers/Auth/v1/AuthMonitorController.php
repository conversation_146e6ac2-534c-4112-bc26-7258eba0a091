<?php

namespace App\Http\Controllers\Auth\v1;

use App\Core\Response\Response;
use App\Enums\ErrorEnum;
use App\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Http\Requests\SystemUser\MonitorLoginRequest;
use App\Models\Giveaway;
use App\Models\Quiz;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AuthMonitorController extends Controller
{
    public function login()
    {
        $input = request()->post();
        $validated = Validator::make($input, MonitorLoginRequest::rules())->validate();

        $model = null;

        $model = match ($validated['type']) {
            'quiz' => Quiz::find($validated['id']),
            'giveaway' => Giveaway::find($validated['id']),
        };

        if (!$model || $model->monitor_pin !== $validated['monitor_pin']) {
            ErrorEnum::INVALID_CREDENTIALS_ERROR->throw();
        }

        match ($validated['type']) {
            'quiz' => Auth::verifyQuiz($model),
            'giveaway' => null,
        };

        $expiresAt = Carbon::now()->addDays(7);

        DB::beginTransaction();

        $token = $model->createToken(
            name: 'app_client|' . Auth::appClient()->id,
            expiresAt: $expiresAt,
        );

        DB::commit();

        return Response::json([
            'data' => $model,
            'token' => $token->plainTextToken,
            'token_expires_at' => $expiresAt,
        ]);
    }
}
