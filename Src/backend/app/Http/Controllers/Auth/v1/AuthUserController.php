<?php

namespace App\Http\Controllers\Auth\v1;

use App\Facades\Auth;
use App\Core\Response\Response;
use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AuthUserController extends Controller
{
    public function fakeRegister()
    {
        $input = [
            'first_name' => '',
            'last_name' => '',
            'email' => 'fakeemail@' . now()->valueOf() . '.' . Str::random(6),
        ];

        DB::beginTransaction();

        $user = UserRepository::save($input);
        $user->email_verified_at = now();
        $user->last_login_ip = request()->ip();
        $user->last_login_device = request()->header('User-Agent');
        $user->last_login_at = now();
        $user->saveOrFail();

        $token = $user->createToken(
            name: 'app_client|' . Auth::appClient()->id,
        );

        DB::commit();

        return Response::json([
            'data' => $user,
            'token' => $token->plainTextToken,
        ], 'El usuario se ha registrado con éxito.');
    }

    public function logout()
    {
        Auth::systemUser()->currentAccessToken()->delete();
        return Response::json(null, 'La sesión ha sido cerrada con éxito.');
    }

    public function me()
    {
        return Response::json([
            'data' => Auth::user(),
        ]);
    }
}
