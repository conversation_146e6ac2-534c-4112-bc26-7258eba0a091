<?php

namespace App\Http\Controllers\Auth\v1;

use App\Core\Response\Response;
use App\Enums\ErrorEnum;
use App\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Http\Requests\SystemUser\QuizLoginRequest;
use App\Models\Quiz;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AuthQuizController extends Controller
{
    public function login()
    {
        $input = request()->post();
        $validated = Validator::make($input, QuizLoginRequest::rules())->validate();

        $quiz = Quiz::find($validated['id']);

        if (!$quiz || $quiz->monitor_pin !== $validated['monitor_pin']) {
            ErrorEnum::INVALID_CREDENTIALS_ERROR->throw();
        }

        Auth::verifyQuiz($quiz);

        $expiresAt = Carbon::now()->addDays(7);

        DB::beginTransaction();

        $token = $quiz->createToken(
            name: 'app_client|' . Auth::appClient()->id,
            expiresAt: $expiresAt,
        );

        DB::commit();

        return Response::json([
            'data' => $quiz,
            'token' => $token->plainTextToken,
            'token_expires_at' => $expiresAt,
        ]);
    }
}
