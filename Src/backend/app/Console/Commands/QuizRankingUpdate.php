<?php

namespace App\Console\Commands;

use App\Data\QuizRankingData;
use App\DynamoDbModels\QuizRanking;
use App\DynamoDbModels\SyncedQuiz;
use App\Enums\QuizTypeEnum;
use App\Models\Quiz;
use App\Storages\RankingStorage;
use App\Websockets\QuizRankingChangedSocket;
use Illuminate\Console\Command;

class QuizRankingUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:quiz:update-ranking {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Se buscan todas las partidas en vivo activas y se actualiza el ranking';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info("Inciando la actualización de los rankings.");

        $updatedRankingsCount = 0;
        $activeQuizzesCount = 0;

        $quizBuilder = Quiz::query();

        if (!$this->option('force')) {
            $quizBuilder->active();
        }

        $quizBuilder->whereType(QuizTypeEnum::Live->name)
            ->chunkById(200, function ($quizzes) use (&$activeQuizzesCount, &$updatedRankingsCount) {
                foreach ($quizzes as $quiz) {
                    $syncedQuiz = SyncedQuiz::find($quiz->id);

                    if ($syncedQuiz) {
                        $activeQuizzesCount++;
                        $oldRanking = $syncedQuiz->ranking;

                        $json = json_encode($syncedQuiz->getFinalRanking()->toArray()['items']);
                        $path = "quiz_ranking:{$quiz->id}.json";

                        if (RankingStorage::instance()->put($path, $json)) {
                            $input['quiz_id'] = $quiz->id;
                            $input['storage_path'] = $path;

                            $newRanking = QuizRankingData::from($input)->all();

                            $oldRanking
                                ? $oldRanking->update($newRanking)
                                : (new QuizRanking())->create($newRanking)
                                ;

                            QuizRankingChangedSocket::dispatch($quiz->id);

                            $updatedRankingsCount++;
                        }
                    }
                }
            });

        $this->info("Se actualizaron $updatedRankingsCount de $activeQuizzesCount partidas activas.");
    }
}
