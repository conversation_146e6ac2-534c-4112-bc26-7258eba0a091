<?php

namespace App\Console\Commands;

use App\DynamoDbModels\SyncedQuiz;
use App\Enums\QuizEventTypeEnum;
use App\Enums\QuizStatusEnum;
use App\Models\Quiz;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class QuizReportGenerate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:quiz:generate-report {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Se generan y se guardan en cache los reportes de las partidas.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info("Inciando la generación de los reportes.");

        $reportsCount = 0;
        $quizzesCount = 0;

        Quiz::chunkById(200, function ($quizzes) use (&$quizzesCount, &$reportsCount) {
            foreach ($quizzes as $quiz) {
                $cache = Cache::get("QuizReport:$quiz->id");
                $quizIsActive = $quiz->status === QuizStatusEnum::Active;
                $quizIsNotFinished = !$quiz->last_event || $quiz->last_event->type !== QuizEventTypeEnum::Finished;

                if ($quizIsActive &&
                    (
                        $this->option('force') ||
                        empty($cache) ||
                        $quizIsNotFinished ||
                        (!empty($cache) && array_key_exists('is_finished', $cache) && !$cache['is_finished'])
                    )
                ) {
                    $syncedQuiz = SyncedQuiz::find($quiz->id);

                    if ($syncedQuiz) {
                        $report = $syncedQuiz->getReport();

                        if ($report) {
                            Cache::put("QuizReport:$syncedQuiz->id", $report);
                            $reportsCount++;
                        };

                        $quizzesCount++;
                    }
                }
            }
        });

        $this->info("Se generaron $reportsCount reportes de $quizzesCount partidas.");
    }
}
