<?php

namespace App\Console\Commands;

use App\Models\Quiz;
use App\Sync\QuizSync;
use Illuminate\Console\Command;

class QuizFinishFinished extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:quiz:finish-finished';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Se buscan todas las partidas activas terminadas por tiempo y se las marca como finalizadas';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $finishedQuizzesCount = 0;
        $activeQuizzesCount = 0;

        Quiz::active()
            ->chunkById(200, function ($quizzes) use (&$activeQuizzesCount, &$finishedQuizzesCount) {
                foreach ($quizzes as $quiz) {
                    $activeQuizzesCount++;

                    if ($quiz->hasStarted() && $quiz->isPlaying() && $quiz->itinerary->steps->last()->endsAt <= now()) {
                        $quiz->finish();
                        (new QuizSync())->run(['model' => $quiz]);
                        $finishedQuizzesCount++;
                    }
                }
            });

        $this->info("Se finalizaron $finishedQuizzesCount de $activeQuizzesCount partidas activas.");
    }
}
