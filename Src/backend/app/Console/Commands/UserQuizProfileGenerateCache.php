<?php

namespace App\Console\Commands;

use App\DynamoDbModels\QuizRanking;
use App\DynamoDbModels\SyncedQuiz;
use App\Enums\QuizEventTypeEnum;
use App\Enums\QuizStatusEnum;
use App\Models\Quiz;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class UserQuizProfileGenerateCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:user-quiz-profile:generate-cache {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Se guardan en cache los perfiles de usuarios.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info("Inciando la generación del cache de los perfiles de usuarios.");

        $quizzesCount = 0;

        Quiz::chunkById(200, function ($quizzes) use (&$quizzesCount) {
            foreach ($quizzes as $quiz) {
                $cache = Cache::get("UserQuizProfiles:$quiz->id");
                $quizIsActive = $quiz->status === QuizStatusEnum::Active;
                $quizIsNotFinished = !$quiz->last_event || $quiz->last_event->type !== QuizEventTypeEnum::Finished;

                if ($quizIsActive &&
                    (
                        $this->option('force') ||
                        empty($cache) ||
                        $quizIsNotFinished ||
                        (!empty($cache) && array_key_exists('is_finished', $cache) && !$cache['is_finished'])
                    )
                ) {
                    $syncedQuiz = SyncedQuiz::find($quiz->id);

                    if ($syncedQuiz) {
                        $userQuizProfiles = $syncedQuiz->user_profiles;

                        foreach ($userQuizProfiles as $profile) {
                            $profile->append(['group', 'last_progress']);

                            if ($profile->group) {
                                $profile['quiz_ranking'] = QuizRanking::where('quiz_id', $profile->quiz_id)->first();
                            }
                        }

                        Cache::put("UserQuizProfiles:$quiz->id", [
                            'data' => $userQuizProfiles,
                            'updated_at' => Carbon::now(),
                            'is_finished' => $syncedQuiz->state === QuizEventTypeEnum::Finished->name,
                        ]);

                        $quizzesCount++;
                    }
                }
            }

            $this->info("Se generó el cache de los perfiles de usuarios de $quizzesCount partidas.");
        });
    }
}
