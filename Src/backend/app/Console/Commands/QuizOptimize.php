<?php

namespace App\Console\Commands;

use App\DynamoDbModels\SyncedQuiz;
use App\Models\Quiz;
use App\Sync\QuizGroupSync;
use App\Sync\QuizSync;
use App\Sync\UserQuizProfileSync;
use App\Sync\UserQuizProgressSync;
use Illuminate\Console\Command;

class QuizOptimize extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:quiz:optimize {quizIds?*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Se crean todas las redundancias para las partidas solicitadas';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $quizBuilder = Quiz::query();

        if (!empty($this->argument('quizIds'))) {
            $quizBuilder->whereIn('id', $this->argument('quizIds'));
        }

        $quizBuilder->chunkById(10, function ($quizzes) {
            foreach ($quizzes as $quiz) {
                if ($syncedQuiz = SyncedQuiz::find($quiz->id)) {
                    $syncedQuiz->delete();
                }

                (new QuizSync())->run(['model' => $quiz, 'avoidSocketDispatch' => true]);

                foreach ($quiz->groups as $group) {
                    (new QuizGroupSync())->run(['model' => $group]);
                }

                $quiz->quiz_users()->chunkById(10, function ($quizUsers) {
                    foreach ($quizUsers as $quizUser) {
                        if ($quizUser->profile) {
                            (new UserQuizProfileSync())->run(['model' => $quizUser->profile, 'avoidSocketDispatch' => true]);
                        }

                        $quizUser->progress()->chunkById(10, function ($progress) {
                            foreach ($progress as $progressValue) {
                                (new UserQuizProgressSync())->run(['model' => $progressValue, 'avoidSocketDispatch' => true]);
                            }
                        });
                    }
                });
            }
        });

        $this->info("Se han optimizado las partidas solicitadas");
    }
}
