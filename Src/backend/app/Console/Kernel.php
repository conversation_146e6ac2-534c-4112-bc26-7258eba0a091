<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        if (!in_array(app()->environment(), ['local', 'production-cron'])) {
            return;
        }

        // https://crontab.guru

        // $schedule->command('inspire')
        //     ->everyMinute()
        //     ->appendOutputTo('storage/logs/laravel-schedule--inspire.log')
        //     ;

        // $schedule->command('telescope:prune')->hourly();

        /* -------------------- */

        $schedule->command('app:quiz:finish-finished')
            ->cron('* * * * *')
            ->withoutOverlapping()
            ->sendOutputTo(storage_path('logs/laravel-schedule--app.quiz.finish-finished.log'))
            ;

        $schedule->command('app:quiz:update-ranking')
            ->everySecond()
            ->withoutOverlapping()
            ->sendOutputTo(storage_path('logs/laravel-schedule--app.quiz.update-ranking.log'))
            ;

        $schedule->command('app:quiz:generate-report')
            ->everyFiveMinutes()
            ->withoutOverlapping()
            ->sendOutputTo(storage_path('logs/laravel-schedule--app.quiz.generate-report.log'))
            ;

        $schedule->command('app:user-quiz-profile:generate-cache')
            ->everyTenMinutes()
            ->withoutOverlapping()
            ->sendOutputTo(storage_path('logs/laravel-schedule--app.user-quiz-profile.generate-cache.log'))
            ;

        $schedule->command('app:media:delete-trashed')->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
