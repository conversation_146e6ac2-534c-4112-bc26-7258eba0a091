<?php

namespace App\Core\Bases;

use BaoPham\DynamoDb\DynamoDbModel;
use Illuminate\Support\Str;

class BaseDynamoDbModel extends DynamoDbModel
{
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        $this->table = 'trivias_fun.' . $this->getTable();
        parent::__construct($attributes);
    }

    /* -------------------- */

    protected static function booting(): void
    {
        static::creating(function (BaseDynamoDbModel $model) {
            if (!$model->id) {
                $model->id = (string) Str::uuid();
            }
        });
    }
}
