<?php

namespace App\Core\Sync;

abstract class SyncModel
{
    protected $_models = [];
    public $config = [];

    public function __construct()
    {
        foreach ($this->_models as $model) {
            $this->config[$model] = [
                'saved',
                'deleted',
            ];
        }
    }

    /* -------------------- */

    abstract public function run(mixed $params);
}

