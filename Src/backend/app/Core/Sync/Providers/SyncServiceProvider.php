<?php

namespace App\Core\Sync\Providers;

use App\Core\Sync\Middleware\RunSync;
use App\Core\Sync\SyncService;
use Illuminate\Contracts\Http\Kernel;
use Illuminate\Support\ServiceProvider;

class SyncServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(Kernel $kernel): void
    {
        $kernel->pushMiddleware(RunSync::class);

        $finder = new \Symfony\Component\Finder\Finder();
        $finder->files()->name('*Sync.php')->in(base_path() . '/app/Sync');

        $namespace = 'App\\Sync';

        foreach ($finder as $file) {
            if ($relativePath = $file->getRelativePath()) {
                $namespace .= '\\' . strtr($relativePath, '/', '\\');
            }

            $class = $namespace . '\\' . $file->getBasename('.php');
            $reflectionClass = new \ReflectionClass($class);

            if (!$reflectionClass->isAbstract()) {
                $syncModel = $reflectionClass->newInstance();

                foreach ($syncModel->config as $modelClass => $events) {
                    foreach ($events as $event) {
                        $modelClass::$event(function ($model) use ($syncModel, $event) {
                            SyncService::add($syncModel, ['model' => $model, 'event' => $event]);
                        });
                    }
                }
            }
        }
    }
}
