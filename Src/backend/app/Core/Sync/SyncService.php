<?php

namespace App\Core\Sync;

class SyncService
{
    private static $_items = [];

    public static function add(SyncModel $syncModel, mixed $params)
    {
        self::$_items[] = [
            'syncModel' => $syncModel,
            'params' => $params,
        ];
    }

    public static function run()
    {
        foreach (self::$_items as $item) {
            $item['syncModel']->run($item['params']);
        }
    }

    public static function clear()
    {
        self::$_items = [];
    }
}

