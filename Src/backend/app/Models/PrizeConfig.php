<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class PrizeConfig extends Model implements HasMedia
{
    use HasFactory,
        InteractsWithMedia;

    protected $appends = [
        'image',
    ];

    /**
     * Relations.
     */
    public function config()
    {
        return $this->belongsTo(ChannelQuizConfig::class, 'config_id');
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function image(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('image'),
        );
    }
}
