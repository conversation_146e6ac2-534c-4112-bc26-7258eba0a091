<?php

namespace App\Models;

use App\Enums\RegisterConfigFieldTypeEnum;
use App\Enums\WhitelistStatusEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Whitelist extends Model
{
    use HasFactory;

    protected $casts = [
        'body' => 'array',
        'status' => WhitelistStatusEnum::class,
        'field_name' => RegisterConfigFieldTypeEnum::class,
    ];

    protected $appends = [
        'status_enum',
        'field_name_enum',
    ];

    /**
     * Relations.
     */
    public function channel_quiz_configs()
    {
        return $this->hasMany(ChannelQuizConfig::class);
    }

    public function quiz_groups()
    {
        return $this->hasMany(QuizGroup::class);
    }


    /**
     * Accesors & Mutators.
     */
    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function fieldNameEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->field_name ? $this->field_name->value() : null,
        );
    }

    /**
     * Scopes.
     */
    public function scopeActive($query)
    {
        return $query->whereStatus(WhitelistStatusEnum::Active->name)
            ->where('body', '!=', '[]')
            ->whereNotNull('body')
            ;
    }
}
