<?php

namespace App\Models;

use App\Facades\Auth;
use App\Features\Itinerary\Itinerary;
use App\Enums\QuizEventTypeEnum;
use App\Enums\QuizModeEnum;
use App\Enums\QuizStatusEnum;
use App\Enums\QuizTypeEnum;
use App\Enums\SystemUserTypeEnum;
use App\Models\Traits\Quiz\QuizTrait;
use App\Websockets\QuizChangedSocket;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;

class Quiz extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    // use SoftDeletes;
    use QuizTrait;

    protected $guard_name = 'quiz';

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'status' => QuizStatusEnum::class,
        'type' => QuizTypeEnum::class,
        'mode' => QuizModeEnum::class,
        'is_public' => 'boolean',
    ];

    protected $appends = [
        'url',
        'qr_url',
        'monitor_url',
        'monitor_record_view_url',
        'state',
        'state_enum',
        'status_enum',
        'type_enum',
        'mode_enum',
        'restarts_at',
        'resuming_at',
    ];

    protected static function booted(): void
    {
        if (Auth::systemUser() && !Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            static::addGlobalScope('authOwnedBy', function (Builder $builder) {
                $builder->whereHas('channel', function ($query) {
                    $query->whereSystemUserId(Auth::systemUser()->id);
                });
            });
        }

        static::addGlobalScope('notSoftDeleted', function (Builder $builder) {
            $builder->whereNull('deleted_at');
        });

        static::saved(function (Quiz $quiz) {
            $quiz->initEvents();

            if ($quiz->question_set_id !== $quiz->getOriginal('question_set_id')) {
                $quiz->refresh()->reassignQuestions();
            }

            // QuizChangedSocket::dispatch($quiz->id);
        });
    }

    /**
     * Relations.
     */
    public function config()
    {
        return $this->belongsTo(ChannelQuizConfig::class, 'config_id');
    }

    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }

    public function questions()
    {
        return $this->belongsToMany(Question::class, 'quiz_questions')
            ->withPivot('order')
            ->orderByPivot('order')
            ->withTimestamps()
            ;
    }

    public function question_set()
    {
        return $this->belongsTo(QuestionSet::class);
    }

    public function prize()
    {
        return $this->belongsTo(QuizPrize::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_quizzes')->withTimestamps();
    }

    public function groups()
    {
        return $this->hasMany(QuizGroup::class, 'quiz_id');
    }

    public function quiz_users()
    {
        return $this->hasMany(UserQuiz::class, 'quiz_id');
    }

    public function user_profiles()
    {
        return $this->hasManyThrough(UserQuizProfile::class, UserQuiz::class);
    }

    public function user_progress()
    {
        return $this->hasManyThrough(UserQuizProgress::class, UserQuiz::class);
    }

    public function events()
    {
        return $this->hasMany(QuizEvent::class);
    }

    public function last_event()
    {
        return $this->hasOne(QuizEvent::class)->latest();
    }

    public function first_event()
    {
        return $this->hasOne(QuizEvent::class)->oldest();
    }

    public function user_profile_register_field_values()
    {
        return $this->hasMany(UserQuizProfileRegisterFieldValue::class, 'quiz_id');
    }

    /**
     * Accesors & Mutators.
     */
    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function typeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->type ? $this->type->value() : null,
        );
    }

    protected function modeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->mode ? $this->mode->value() : null,
        );
    }

    protected function state(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->last_event->type->name ?? QuizEventTypeEnum::Playing->name,
        );
    }

    protected function stateEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => ($this->last_event->type ?? QuizEventTypeEnum::Playing)->value(),
        );
    }

    protected function url(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_url') . '/' . $this->channel?->slug . '/' . $this->id,
        );
    }

    protected function qrUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->url . '/{qr}',
        );
    }

    protected function monitorUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_monitor_url') . '/' . $this->channel?->slug . '/' . $this->id,
        );
    }

    protected function monitorRecordViewUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_monitor_url') . '/' . $this->channel?->slug . '/' . $this->id . '/vista-grabacion',
        );
    }

    protected function startsAt(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => $value ? (new Carbon($value))->startOfMinute() : null,
        );
    }

    protected function endsAt(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => $value ? (new Carbon($value))->startOfMinute() : null,
        );
    }

    protected function canAccess(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->is_public
                ? true
                : (Auth::user()
                    ? $this->users()->where('users.id', Auth::user()->id)->exists()
                    : false),
        );
    }

    protected function authUserProfile(): Attribute
    {
        return Attribute::make(
            get: fn () => Auth::user()
                ? Auth::user()->profile_on_quiz($this->id)->with('register_field_values.field')->first()
                : null,
        );
    }

    protected function authUserProgress(): Attribute
    {
        return Attribute::make(
            get: fn () => Auth::user()
                ? Auth::user()->progress_on_quiz($this->id)->with('selected_answer')->get()
                : null,
        );
    }

    protected function itinerary(): Attribute
    {
        return Attribute::make(
            get: fn () => new Itinerary($this, Auth::user()),
        );
    }

    protected function rawItinerary(): Attribute
    {
        return Attribute::make(
            get: fn () => new Itinerary($this),
        );
    }

    protected function ranking(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFinalRanking(),
        );
    }

    protected function hasRoom(): Attribute
    {
        return Attribute::make(
            get: fn () => !!$this->config->max_users_per_quiz
                ? $this->users->count() < $this->config->max_users_per_quiz
                : true
        );
    }

    /**
     * Scopes
     */
    public function scopeWithConfigFull($query)
    {
        return $query->with('config.multimedia_config', 'config.style_config', 'config.whitelist', 'config.prize_configs', 'config.register_config_fields');
    }

    public function scopeActive($query)
    {
        return $query->whereStatus(QuizStatusEnum::Active->name)
            ->where(function ($query) {
                return $query
                    ->doesntHave('last_event')
                    ->orWhereDoesntHave('last_event', function ($query) {
                        return $query->where('type', QuizEventTypeEnum::Finished->name);
                    })
                    ;
            });
    }
}
