<?php

namespace App\Models;

use App\Facades\Auth;
use App\Enums\ErrorEnum;
use App\Enums\LanguageEnum;
use App\Enums\ChannelStatusEnum;
use App\Enums\SystemUserTypeEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Channel extends Model
{
    use HasFactory,
        SoftDeletes;

    protected $casts = [
        'status' => ChannelStatusEnum::class,
        'lang' => LanguageEnum::class,
        'is_protected' => 'boolean',
    ];

    protected $appends = [
        'url',
        'next_quiz_url',
        'monitor_url',
        'webapp_tyc_url',
        'status_enum',
        'lang_enum',
    ];

    protected static function booted(): void
    {
        if (Auth::systemUser() && !Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            static::addGlobalScope('authOwnedBy', function (Builder $builder) {
                $builder->whereSystemUserId(Auth::systemUser()->id);
            });
        }

        static::updating(function (Channel $channel) {
            if ($channel->getOriginal('is_protected') && $channel->slug !== $channel->getOriginal('slug')) {
                ErrorEnum::CHANNEL_UPDATE_SLUG_PROTECTED_ERROR->throw();
            }
        });

        static::deleting(function (Channel $channel) {
            if ($channel->is_protected) {
                ErrorEnum::CHANNEL_DELETE_PROTECTED_ERROR->throw();
            }

            if ($channel->quizzes()->whereNull('deleted_at')->get()->isNotEmpty()) {
                ErrorEnum::CHANNEL_DELETE_HAS_QUIZ_ERROR->throw();
            }
        });
    }

    /**
     * Relations.
     */
    public function config()
    {
        return $this->belongsTo(ChannelQuizConfig::class, 'config_id');
    }

    public function system_user()
    {
        return $this->belongsTo(SystemUser::class);
    }

    public function quizzes()
    {
        return $this->hasMany(Quiz::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function langEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->lang ? $this->lang->value() : null,
        );
    }

    protected function quizzesCount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->quizzes ? $this->quizzes->count() : null,
        );
    }

    protected function url(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_url') . '/' . $this->slug,
        );
    }

    protected function nextQuizUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_url') . '/' . $this->slug . '/proxima-partida'
        );
    }

    protected function monitorUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_monitor_url') . '/' . $this->slug,
        );
    }

    protected function webappTycUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_tyc_url'),
        );
    }

    /**
     * Scopes
     */
    public function scopeWithConfigFull($query)
    {
        return $query->with('config.multimedia_config', 'config.style_config', 'config.prize_configs', 'config.register_config_fields');
    }
}
