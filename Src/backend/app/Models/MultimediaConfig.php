<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class MultimediaConfig extends Model implements HasMedia
{
    use HasFactory,
        InteractsWithMedia;

    protected $appends = [
        'logo',
        'bg_video',
        'bg_image',
        'monitor_bg_video',
        'monitor_bg_image',
        'banner',
    ];

    /**
     * Relations.
     */
    public function channel_quiz_configs()
    {
        return $this->hasMany(ChannelQuizConfig::class);
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')->singleFile();
        $this->addMediaCollection('bg_video')->singleFile();
        $this->addMediaCollection('bg_image')->singleFile();
        $this->addMediaCollection('monitor_bg_video')->singleFile();
        $this->addMediaCollection('monitor_bg_image')->singleFile();
        $this->addMediaCollection('banner')->singleFile();
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function logo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('logo'),
        );
    }

    protected function bgVideo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('bg_video'),
        );
    }

    protected function bgImage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('bg_image'),
        );
    }

    protected function monitorBgVideo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('monitor_bg_video'),
        );
    }

    protected function monitorBgImage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('monitor_bg_image'),
        );
    }

    protected function banner(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('banner'),
        );
    }
}
