<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class GiveawayPrize extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $appends = [
        'image',
    ];

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
    }

    /**
     * Relations.
     */
    public function giveaway()
    {
        return $this->belongsTo(Giveaway::class);
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function image(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('image'),
        );
    }
}
