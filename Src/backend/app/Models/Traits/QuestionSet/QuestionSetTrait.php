<?php

namespace App\Models\Traits\QuestionSet;

trait QuestionSetTrait
{
    // use QuestionSetTrait;

    /* -------------------- */

    public function reassignQuestionsQuizzes(): void
    {
        $quizzes = $this->quizzes()->get();

        foreach ($quizzes as $quiz) {
            $quiz->reassignQuestions();
        }
    }

    public function getQuestionsForAssignment(): Array
    {
        $questions = $this->questions()->get();

        foreach ($questions as $question) {
            $question->pivot->makeHidden(['set_id', 'question_id', 'created_at', 'updated_at']);
        }

        return $questions->toArray();
    }
}
