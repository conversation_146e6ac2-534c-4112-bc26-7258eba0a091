<?php

namespace App\Models\Traits\UserQuizProgress;

use App\Enums\QuestionTypeMultiplierEnum;
use App\Models\UserQuizProgress;

trait UserQuizProgressTrait
{

    public function calcPoints(): void
    {
        $pointsMultiplier = QuestionTypeMultiplierEnum::tryFromValue($this->question->type->value()['multiplier']);
        $pointsBaseEarned = 10000;
        $pointsBaseEarned = 0;
        $pointsEarned = ($pointsBaseEarned + $this->points_earned) * $pointsMultiplier->value();

        if (!$this->selected_answer->is_correct) {
            $pointsEarned = $this->user_quiz->quiz->config->allow_lose_points ? $pointsEarned * -1 : 0;
        }

        $pointsBefore = UserQuizProgress::where('user_quiz_id', $this->user_quiz_id)->latest()->first()->points_now ?? 0;
        $pointsNow = $pointsEarned + $pointsBefore;

        if ($pointsNow < 0 && !$this->user_quiz->quiz->config->allow_negative_points) {
            $pointsNow = 0;
        }

        $this->points_multiplier = $pointsMultiplier->name;
        $this->points_base_earned = $pointsBaseEarned;
        $this->points_earned = $pointsEarned;
        $this->points_before = $pointsBefore;
        $this->points_now = $pointsNow;
    }
}
