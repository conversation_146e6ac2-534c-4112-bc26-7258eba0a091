<?php

namespace App\Models\Traits\Quiz;

use App\Enums\ErrorEnum;
use App\Models\User;

trait QuizGroupTrait
{
    public function allocateUserInGroup(User $user): void
    {
        if (!$this->groups()->exists()) return;

        if ($user->group_on_quiz($this->id)->exists()) return;

        $userProfile = $user->profile_on_quiz($this->id)->first();
        $userInWhitelist = false;

        foreach ($this->groups as $group) {
            $whitelist = $group->whitelist()->active()->first();

            if ($whitelist) {
                if (in_array($userProfile->register_field_values->firstWhere('field.type', $whitelist->field_name)->value, $whitelist->body)) {
                    $userInWhitelist = true;

                    if (!$group->max_users || $group->users->count() < $group->max_users) {
                        $user->groups()->attach($group);
                        $user->saveOrFail();

                        // $quizRedis = New QuizRedis;
                        // $quizRedis->run($this);

                        break;
                    } else {
                        ErrorEnum::QUIZ_GROUP_MAX_USERS_LIMIT_REACHED_ERROR->throw();
                    }
                }
            }
        }

        if (!$userInWhitelist) {
            ErrorEnum::QUIZ_WHITELIST_ERROR->throw();
        }
    }
}
