<?php

namespace App\Models\Traits\Quiz;

use App\Enums\QuestionTypeEnum;

trait QuizReportTrait
{
    public function getReport(bool $withAnswerUsersReport = false): Array
    {
        $report = [];

        $report['is_multiple'] = false;
        $report['use_groups'] = !$this->groups->isEmpty();
        $report['quiz_type_enum'] = $this->typeEnum;
        $report['quiz_last_event'] = $this->last_event;
        $report['questions_count']['total'] = $this->questions->count();
        $report['questions_count'][QuestionTypeEnum::Double->name] = $this->questions->where('type', QuestionTypeEnum::Double)->count();
        $report['questions_count'][QuestionTypeEnum::Bomb->name] = $this->questions->where('type', QuestionTypeEnum::Bomb)->count();
        $report['questions_count'][QuestionTypeEnum::Survey->name] = $this->questions->where('type', QuestionTypeEnum::Survey)->count();

        foreach ($this->questions as $question) {
            $reportQuestion = $question->toArray();
            $reportQuestion['users_count'] = 0;

            foreach ($question->answers as $answer) {
                $reportAnswer = $answer->toArray();
                $reportAnswer['users_count'] = $this->user_progress->where('selected_answer_id', $answer->id)->count();
                $reportQuestion['users_count'] += $reportAnswer['users_count'];

                if ($withAnswerUsersReport) {
                    $reportAnswer['users'] = $this->getAnswerUsersReport($answer->id);
                }

                $reportQuestion['answers'][] = $reportAnswer;
            }

            $reportQuestion = $this->getAnswersPercentage($reportQuestion);

            $report['questions'][] = $reportQuestion;
        }

        $report['ranking'] = $this->getFinalRanking();

        return $report;
    }

    public function getAnswerUsersReport($answerId): Array
    {
        $report = collect();

        foreach ($this->user_progress->where('selected_answer_id', $answerId) as $progress) {
            $profile = $progress->user_quiz->profile;

            $report->push([
                'name' => $profile->name,
                'email' => $profile->email,
                'points' => $progress->points_earned,
            ]);
        }

        $report = $report
            ->sortByDesc('points')->values()
            ->toArray();

        return $report;
    }

    public function getAnswersPercentage($question) {
        $question['users_count_correct_percentage'] = 0;
        $question['users_count_incorrect_percentage'] = 0;

        foreach ($question['answers'] as $key => $answer) {
            $question['answers'][$key]['users_count_percentage'] = $question['users_count'] > 0
                ? ($answer['users_count'] / $question['users_count']) * 100
                : 0;

            $question['answers'][$key]['is_correct']
                ? $question['users_count_correct_percentage'] += $question['answers'][$key]['users_count_percentage']
                : $question['users_count_incorrect_percentage'] += $question['answers'][$key]['users_count_percentage'];
        }

        return $question;
    }

    /* -------------------- */

    public static function getReports(): Array
    {
        $report = [];

        $report['is_multiple'] = true;

        //

        return $report;
    }
}
