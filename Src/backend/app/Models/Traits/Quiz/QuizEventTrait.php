<?php

namespace App\Models\Traits\Quiz;

use App\Enums\ErrorEnum;
use App\Enums\QuizEventTypeEnum;
use App\Enums\QuizModeEnum;
use App\Enums\QuizTypeEnum;
use App\Models\QuizEvent;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Throwable;

trait QuizEventTrait
{
    private $_resumingSecondsForStart = 10;
    private $_resumingSecondsForPlaying = 5;

    private function _getPausedSeconds(): int
    {
        $pausedSeconds = 0;
        $events = $this->events()->get();

        foreach($events as $key => $event) {
            if ($key === 0 || !$event->type->is(QuizEventTypeEnum::Playing)) {
                continue;
            }

            $pausedSeconds = $pausedSeconds + $event->created_at->diffInSeconds($events[$key - 1]->created_at);
        }

        return $pausedSeconds;
    }

    private function _getDefaultResumingSeconds(): int
    {
        $firstEvent = $this->first_event()->first();
        $lastEvent = $this->last_event()->first();

        if (!$lastEvent) {
            return 0;
        }

        if ($firstEvent->id === $lastEvent->id) {
            if ($firstEvent->created_at > $this->starts_at) {
                return $this->_resumingSecondsForStart;
            }

            return 0;
        }

        if ($lastEvent->type->is(QuizEventTypeEnum::Playing)) {
            return $this->_resumingSecondsForPlaying;
        }

        return 0;
    }

    private function _getResumingSeconds(): int
    {
        $resumingSeconds = 0;
        $events = $this->events()->get();

        foreach($events as $key => $event) {
            if ($key === 0) {
                if ($event->created_at > $this->starts_at) {
                    $resumingSeconds = $resumingSeconds + $this->_resumingSecondsForStart;
                }
            }

            if (!$event->type->is(QuizEventTypeEnum::Paused)) {
                continue;
            }

            $resumingSeconds = $resumingSeconds + $this->_resumingSecondsForPlaying;
        }

        return $resumingSeconds;
    }

    /* -------------------- */

    protected function restartsAt(): Attribute
    {
        return Attribute::make(
            get: function () {
                $restartsAt = $this->starts_at->clone();

                if ($this->type->is(QuizTypeEnum::OnDemand)) {
                    return $restartsAt;
                }

                if ($this->first_event && $this->first_event->created_at >= $restartsAt) {
                    $restartsAt = $this->first_event->created_at->clone();
                }

                $restartsAt->addSeconds($this->_getPausedSeconds());
                $restartsAt->addSeconds($this->_getResumingSeconds());

                return $restartsAt;
            },
        );
    }

    protected function resumingAt(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->type->is(QuizTypeEnum::OnDemand)) {
                    return null;
                }

                return $this->last_event && $this->last_event->type->is(QuizEventTypeEnum::Playing)
                    ? $this->last_event->created_at->clone()->addSeconds($this->_getDefaultResumingSeconds())
                    : null;
            },
        );
    }

    /* -------------------- */

    private function _setEvent(QuizEventTypeEnum $eventType): void
    {
        $event = new QuizEvent();
        $event->type = $eventType;
        $event->quiz()->associate($this);

        $event->saveOrFail();
    }

    /* -------------------- */

    public function play(): void
    {
        if (!$this->hasStarted() || $this->isPaused()) {
            $this->_setEvent(QuizEventTypeEnum::Playing);
            return;
        }

        ErrorEnum::QUIZ_SET_EVENT_ERROR->throw();
    }

    public function pause(): void
    {
        if ($this->type->is(QuizTypeEnum::OnDemand)) {
            ErrorEnum::QUIZ_SET_EVENT_NOT_ALLOWED_ERROR->throw();
        }

        if ($this->isPlaying()) {
            if ($this->first_event->id === $this->last_event->id) {
                if ($this->first_event->created_at > $this->starts_at && $this->first_event->created_at->addSeconds($this->_resumingSecondsForStart) > now()) {
                    $this->first_event->delete();
                } else {
                    $this->_setEvent(QuizEventTypeEnum::Paused);
                }
            } else {
                if ($this->last_event->created_at->addSeconds($this->_resumingSecondsForPlaying) > now()) {
                    $this->last_event->delete();
                } else {
                    $this->_setEvent(QuizEventTypeEnum::Paused);
                }
            }

            return;
        }

        ErrorEnum::QUIZ_SET_EVENT_ERROR->throw();
    }

    public function finish(): void
    {
        if ($this->hasStarted() && !$this->isFinished()) {
            $this->_setEvent(QuizEventTypeEnum::Finished);
            return;
        }

        ErrorEnum::QUIZ_SET_EVENT_ERROR->throw();
    }

    /* -------------------- */

    public function hasStarted(): bool
    {
        return !!$this->last_event;
    }

    public function isPlaying(): bool
    {
        if (!$this->hasStarted()) {
            return false;
        }

        return QuizEventTypeEnum::Playing->is($this->last_event->type);
    }

    public function isPaused(): bool
    {
        if (!$this->hasStarted()) {
            return false;
        }

        return QuizEventTypeEnum::Paused->is($this->last_event->type);
    }

    public function isFinished(): bool
    {
        if (!$this->hasStarted()) {
            return false;
        }

        return QuizEventTypeEnum::Finished->is($this->last_event->type);
    }

    /* -------------------- */

    public function initEvents(): void {
        if ($this->isDirty('type') || $this->isDirty('mode')) {
            $this->clearEvents();
        }

        if ($this->type->is(QuizTypeEnum::OnDemand) || $this->mode->is(QuizModeEnum::Scheduled)) {
            try {
                $this->play();
            } catch (Throwable $e) {}
        }
    }

    public function clearEvents(): void {
        $this->events()->delete();
    }
}
