<?php

namespace App\Models\Traits\Quiz;

use App\Core\Redis\Redis;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use App\Features\Itinerary\ItineraryStep;
use Illuminate\Support\Collection;

trait QuizRankingTrait
{
    public function getPartialRanking(?ItineraryStep $step = null): ?Collection
    {
        if ($step) {
            if (!$step->type->in([
                ItineraryStepTypeEnum::QuestionResults,
                ItineraryStepTypeEnum::Ranking,
                ItineraryStepTypeEnum::FinalRanking,
                ItineraryStepTypeEnum::Results,
                ItineraryStepTypeEnum::FinalResults,
            ])) {
                return null;
            }
        }

        if ($this->groups->isEmpty()) {
            $rankingItems = $this->_getRankingItems($step);
        } else {
            $rankingItems = $this->_getRankingItemsByGroup($step);
        }

        // for ($i = 0; $i < 20; $i++) {
        //     $rankingItems->add(collect([
        //         'user_profile_id' => -1,
        //         'name' => 'Test',
        //         'points' => rand(0, 100000),
        //         'its-me' => $i === 0 ? true : false,
        //     ]));
        // }

        if ($rankingItems) {
            $rankingItems = $rankingItems
                ->sortByDesc('points')->values()
                ->map(function ($item, $index) {
                    return $item->put('position', $index + 1);
                })
                ;
        }

        return collect([
            'items' => $rankingItems,
        ]);
    }

    public function getFinalRanking(): ?Collection
    {
        return $this->getPartialRanking();
    }

    /* -------------------- */

    private function _getRankingItems(?ItineraryStep $step = null): ?Collection
    {
        return $this->quiz_users()
            ->has('profile')
            ->get()
            ->map(function ($quizUser) use ($step) {
                return collect([
                    'user_profile_id' => $quizUser->profile->id,
                    'name' => $quizUser->profile->name,
                    'email' => $quizUser->profile->email,
                    'extra_register_data' => $quizUser->profile->extra_register_data_for_ranking,
                    'points' => $step
                        ? $quizUser->progress->reverse()->firstWhere('step_id', '<=', $step->id)->points_now ?? 0
                        : $quizUser->progress->last()->points_now ?? 0,
                    'podium' => $quizUser->quiz->podium,
                ]);
            })
            ;
    }

    private function _getRankingItemsByGroup(?ItineraryStep $step = null): ?Collection
    {
        return $this->groups
            ->map(function ($group) use ($step) {
                return collect([
                    'quiz_group_id' => $group->id,
                    'users_in_group_count' => $group->users->count(),
                    'name' => $group->name,
                    'points' => round($group->users
                        ->map(function ($user) use ($step) {
                            return collect([
                                'points' => $step
                                    ? $user->progress_on_quiz($this->id)->get()->reverse()->firstWhere('step_id', '<=', $step->id)->points_now ?? 0
                                    : $user->progress_on_quiz($this->id)->get()->last()->points_now ?? 0,
                            ]);
                        })->avg('points') ?? 0),
                    'podium' => $this->podium,
                ]);
            })
            ;
    }

    /* -------------------- */

    private function _getRankingItemsRedis(?ItineraryStep $step = null): ?Collection
    {
        // $userQuizProfiles = Redis::filter("channel:{$this->channel_id}:quiz:{$this->id}:user:*:profile:*");
        $userQuizProfiles = collect(Redis::get("channel:{$this->channel_id}:quiz:{$this->id}:profiles"));

        return $userQuizProfiles
            ->map(function ($userQuizProfile) use ($step) {
                // $userQuizProgress = Redis::filter("channel:{$this->channel_id}:quiz:{$this->id}:user:{$userQuizProfile['user_quiz']['user_id']}:progress:*")->sortBy('id');
                $userQuizProgress = collect(Redis::get("channel:{$this->channel_id}:quiz:{$this->id}:user:{$userQuizProfile['user_quiz']['user_id']}:manyProgress"))->sortBy('id');

                return collect([
                    'user_profile_id' => $userQuizProfile['id'],
                    'name' => $userQuizProfile['name'],
                    'email' => $userQuizProfile['email'],
                    'extra_register_data' =>$userQuizProfile['extra_register_data_for_ranking'],
                    'points' => $step
                        ? collect($userQuizProgress)->reverse()->firstWhere('step_id', '<=', $step->id)['points_now'] ?? 0
                        : collect($userQuizProgress)->last()['points_now'] ?? 0,
                    'podium' => $this->podium,
                ]);
            });
    }

    private function _getRankingItemsByGroupRedis(?ItineraryStep $step = null): ?Collection
    {
        if (!$quiz = Redis::find("channel:{$this->channel_id}:quiz:{$this->id}")) {
            return null;
        }

        return collect($quiz['groups'])
            ->map(function ($group) use ($step) {
                return collect([
                    'quiz_group_id' => $group['id'],
                    'users_in_group_count' => collect($group['users'])->count(),
                    'name' => $group['name'],
                    'points' => round(collect($group['users'])
                        ->map(function ($user) use ($step) {
                            $userQuizProgress = Redis::filter("channel:{$this->channel_id}:quiz:{$this->id}:user:{$user['id']}:progress:*")->sortBy('id');
                            $userQuizProgress = Redis::filter("channel:{$this->channel_id}:quiz:{$this->id}:user:{$user['id']}:progress:*")->sortBy('id');

                            return collect([
                                'points' => $step
                                ? collect($userQuizProgress)->reverse()->firstWhere('step_id', '<=', $step->id)['points_now'] ?? 0
                                : collect($userQuizProgress)->last()['points_now'] ?? 0,
                            ]);
                        })->avg('points') ?? 0),
                    'podium' => $this->podium,
                ]);
            });
    }
}
