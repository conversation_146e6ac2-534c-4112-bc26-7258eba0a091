<?php

namespace App\Models\Traits\Quiz;

use App\Enums\ErrorEnum;
use App\Enums\QuizStatusEnum;
use App\Models\Quiz;
use App\Repositories\QuizRepository;

trait QuizTrait
{
    use QuizEventTrait, QuizQuestionTrait, QuizRankingTrait, QuizGroupTrait, QuizReportTrait;

    /* -------------------- */

    public function verifyStatus()
    {
        if (!$this->status->is(QuizStatusEnum::Active)) {
            ErrorEnum::INACTIVE_USER_ERROR->throw();
        }
    }

    /* -------------------- */

    public function generatePin(): void
    {
        do {
            $this->pin = 'Q' . mt_rand(10000, 99999);
        } while (Quiz::where('pin', $this->pin)->exists());

        $this->saveOrFail();
    }

    public function generateMonitorPin(): void
    {
        do {
            $this->monitor_pin = 'M' . mt_rand(10000, 99999);
        } while (Quiz::where('monitor_pin', $this->monitor_pin)->exists());

        $this->saveOrFail();
    }

    /* -------------------- */

    public static function duplicate(int $quizId): Quiz
    {
        $quiz = Quiz::with(['channel', 'question_set'])->withConfigFull()->findOrFail($quizId);

        $input = $quiz->makeHidden(['id', 'config_id'])->toArray();
        $input['name'] = $input['name'] . '_copia';
        $input['config'] = $quiz->config->makeHidden(['id', 'multimedia_config_id', 'style_config_id'])->toArray();
        $input['config']['multimedia_config'] = $quiz->config->multimedia_config->makeHidden(['id'])->toArray();
        $input['config']['style_config'] = $quiz->config->style_config->makeHidden(['id'])->toArray();
        $input['config']['prize_configs'] = $quiz->config->prize_configs->map(fn ($item) => $item->makeHidden(['id', 'config_id']))->toArray();
        $input['config']['register_config_fields'] = $quiz->config->register_config_fields->map(fn ($item) => $item->makeHidden(['id', 'config_id']))->toArray();
        $input['config']['whitelist'] = $quiz->config->whitelist->makeHidden(['id'])->toArray();

        $input['groups'] = $quiz->groups->load('whitelist')->map(function ($item) {
            $item->makeHidden(['id', 'quiz_id', 'whitelist_id']);
            $item->whitelist->makeHidden(['id']);

            return $item;
        })->toArray();

        if ($quiz->starts_at->isPast()) {
            $input['starts_at'] = now()->addMinutes(10);
            $input['ends_at'] = $quiz->ends_at ? $input['starts_at']->clone()->addSeconds($quiz->starts_at->diffInSeconds($quiz->ends_at)) : null;
        }

        return QuizRepository::save($input);
    }
}
