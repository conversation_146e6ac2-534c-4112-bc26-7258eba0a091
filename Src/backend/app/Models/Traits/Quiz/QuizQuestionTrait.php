<?php

namespace App\Models\Traits\Quiz;

use App\Repositories\QuizRepository;

trait QuizQuestionTrait
{

    public function reassignQuestions(): void
    {
        $input['questions'] = $this->question_set()->first()->getQuestionsForAssignment();
        QuizRepository::save($input, $this);
    }

    /* -------------------- */

    public function getRandomQuestionIds(): array
    {
        $questionIds = $this->questions()
            ->pluck('questions.id')
            ->shuffle()
            ;

        if ($this->config->amount_random_questions) {
            $questionIds = $questionIds->take($this->config->amount_random_questions);
        } else {
            $questionIds = $questionIds->split(2)[0];
        }

        return $questionIds->toArray();
    }
}
