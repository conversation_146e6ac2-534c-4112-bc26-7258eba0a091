<?php

namespace App\Models\Traits\Giveaway;

use App\DynamoDbModels\QuizRanking;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Models\Quiz;

trait GiveawayTrait
{
    public function generatePin(): void
    {
        do {
            $this->pin = 'S' . mt_rand(10000, 99999);
        } while (Quiz::where('pin', $this->pin)->exists());

        $this->saveOrFail();
    }

    /* -------------------- */

    public function resolveAndSaveParticipants(): void
    {
        if (!$this->quiz) return;

        $quizRanking = QuizRanking::where('quiz_id', $this->quiz->id)->first();
        $participants= collect($quizRanking->items);

        $participants = match ($this->allowed_participants_mode) {
            GiveawayAllowedParticipantsModeEnum::All => $participants,
            GiveawayAllowedParticipantsModeEnum::NonWinners => $participants->filter(fn ($participant) => !$participant['podium']),
            GiveawayAllowedParticipantsModeEnum::CorrectAnswers => $participants->filter(fn ($participant) => $participant['points'] >= $this->min_correct_answers),
        };

        $this->participants = $participants->map(function ($participant) {
            return [
                'id' => $participant['user_profile_id'],
                'name' => $participant['name'],
                'email' => $participant['email'],
                'custom_fields' => $participant['custom_fields'],
                'position' => $participant['position'],
            ];
        })->values()->all();

        $this->saveOrFail();
    }
}
