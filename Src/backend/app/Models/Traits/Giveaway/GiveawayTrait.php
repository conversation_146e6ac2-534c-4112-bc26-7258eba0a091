<?php

namespace App\Models\Traits\Giveaway;

use App\DynamoDbModels\QuizRanking;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Enums\GiveawayStatusEnum;
use App\Models\Giveaway;

trait GiveawayTrait
{
    public function generatePin(): void
    {
        do {
            $this->monitor_pin = 'S' . mt_rand(10000, 99999);
        } while (Giveaway::where('monitor_pin', $this->monitor_pin)->exists());

        $this->saveOrFail();
    }

    /* -------------------- */

    public function resolveAndSaveParticipants(): void
    {
        if (!$this->quiz) return;

        $quizRanking = QuizRanking::where('quiz_id', $this->quiz->id)->first();
        $participants= collect($quizRanking->items);

        $participants = match ($this->allowed_participants_mode) {
            GiveawayAllowedParticipantsModeEnum::All => $participants,
            GiveawayAllowedParticipantsModeEnum::NonWinners => $participants->filter(fn ($participant) => $participant['position'] > $participant['podium']),
            GiveawayAllowedParticipantsModeEnum::CorrectAnswers => $participants->filter(fn ($participant) => $participant['points'] >= $this->min_correct_answers),
        };

        $this->participants = $participants->map(function ($participant) {
            return [
                'id' => $participant['user_profile_id'],
                'name' => ucfirst(strtolower($participant['name'])),
                'email' => $participant['email'],
                'custom_fields' => $participant['custom_fields'],
                'position' => $participant['position'],
                'points' => $participant['points'],
            ];
        })->values()->all();

        $this->saveOrFail();
    }

    public function resolveAndSaveWinners(): void
    {
        if ($this->status !== GiveawayStatusEnum::Pending) {
            return;
        }

        $participants = collect($this->participants);
        $allWinners = [];

        foreach ($this->prizes as $prize) {
            $participants = $participants->filter(function ($participant) use ($allWinners) {
                return !collect($allWinners)->firstWhere('id', $participant['id']);
            });

            $winners = $participants->shuffle()->take($prize->quantity)
                ->map(function ($winner) use ($prize) {
                    return [
                        'prize_id' => $prize->id,
                        'prize_name' => $prize->name,
                        'id' => $winner['id'],
                        'name' => $winner['name'],
                        'email' => $winner['email'],
                        'custom_fields' => $winner['custom_fields'],
                        'position' => $winner['position'],
                        'points' => $winner['points'],
                        'revealed' => false,
                    ];
                })->values()->all();

            $allWinners = array_merge($allWinners, $winners);
        }

        $this->winners = $allWinners;
        $this->saveOrFail();
    }

    public function allWinnersRevealed(): bool
    {
        return collect($this->winners)->every(fn ($winner) => $winner['revealed']);
    }
}
