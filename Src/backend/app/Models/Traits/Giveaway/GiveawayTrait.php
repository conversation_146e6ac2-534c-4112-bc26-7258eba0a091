<?php

namespace App\Models\Traits\Giveaway;

use App\Models\Giveaway;

trait GiveawayTrait
{
    public function generatePin(): void
    {
        do {
            $this->monitor_pin = 'S' . mt_rand(10000, 99999);
        } while (Giveaway::where('monitor_pin', $this->monitor_pin)->exists());

        $this->saveOrFail();
    }

    /* -------------------- */

    public function allWinnersRevealed(): bool
    {
        return collect($this->winners)->every(fn ($winner) => $winner['revealed']);
    }
}
