<?php

namespace App\Models\Traits\UserQuizProfile;

use App\Enums\RegisterConfigFieldTypeEnum;

trait UserQuizProfileScopeFilterTrait
{

    public function scopeFilterByName($query, $name)
    {
        return $query->whereHas('register_field_values', function ($query) use ($name) {
            return $query->where('value', 'like', '%' . $name .'%')
                ->whereHas('field', function ($query) {
                    return $query->whereType(RegisterConfigFieldTypeEnum::DefaultName->name);
                });
        });
    }

    public function scopeFilterByEmail($query, $email)
    {
        return $query->whereHas('register_field_values', function ($query) use ($email) {
            return $query->where('value', 'like', '%' . $email .'%')
                ->whereHas('field', function ($query) {
                    return $query->whereType(RegisterConfigFieldTypeEnum::DefaultEmail->name);
                });
        });
    }

}
