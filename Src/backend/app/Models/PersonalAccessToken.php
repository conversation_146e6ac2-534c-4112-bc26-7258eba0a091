<?php

namespace App\Models;

use Illuminate\Support\Facades\Cache;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;
use Illuminate\Support\Str;

class PersonalAccessToken extends SanctumPersonalAccessToken
{
    /**
     * Limit saving of PersonalAccessToken records
     *
     * We only want to actually save when there is something other than
     * the last_used_at column that has changed. It prevents extra DB writes
     * since we aren't going to use that column for anything.
     *
     * @param  array  $options
     * @return bool
     */
    public function save(array $options = [])
    {
        $changes = $this->getDirty();

        // Check for 2 changed values because one is always the updated_at column
        if (!array_key_exists('last_used_at', $changes) || count($changes) > 2) {
            parent::save();
        }

        return false;
    }

    /* -------------------- */

    public static function findToken($token)
    {
        $path = Str::finish(request()->path(), '/');

        if (Str::startsWith($path, 'api/v1/webapp/')) {
            $token = Cache::remember("PersonalAccessToken:$token", 600, function () use ($token) {
                return parent::findToken($token) ?? '_null_';
            });

            if ($token === '_null_') {
                return null;
            }

            return $token;
        }

        return parent::findToken($token);
    }

    public function getTokenableAttribute()
    {
        $path = Str::finish(request()->path(), '/');

        if (Str::startsWith($path, 'api/v1/webapp/')) {
            return Cache::remember("PersonalAccessToken:{$this->id}:tokenable", 600, function () {
                return parent::tokenable()->first();
            });
        }

        return parent::tokenable()->first();
    }
}
