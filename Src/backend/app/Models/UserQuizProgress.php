<?php

namespace App\Models;

use App\Enums\QuestionTypeMultiplierEnum;
use App\Models\Traits\UserQuizProgress\UserQuizProgressTrait;
use App\Websockets\QuizRankingChangedSocket;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserQuizProgress extends Model
{
    use HasFactory,
        UserQuizProgressTrait;

    protected $casts = [
        'points_multiplier' => QuestionTypeMultiplierEnum::class,
    ];

    protected $appends = [
        'points_multiplier_enum',
    ];

    protected static function booted(): void
    {
        static::saving(function (UserQuizProgress $userQuizProgress) {
            $userQuizProgress->calcPoints();
        });

        static::saved(function (UserQuizProgress $userQuizProgress) {
            // QuizRankingChangedSocket::dispatch($userQuizProgress->user_quiz->quiz);
        });
    }

    /**
     * Relations.
     */
    public function user_quiz()
    {
        return $this->belongsTo(UserQuiz::class);
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function selected_answer()
    {
        return $this->belongsTo(Answer::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function pointsMultiplierEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->points_multiplier ? $this->points_multiplier->value() : null,
        );
    }
}
