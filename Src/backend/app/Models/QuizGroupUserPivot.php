<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class QuizGroupUserPivot extends Pivot
{
    use HasFactory;

    protected $table = 'quiz_group_users';

    /**
     * Relations.
     */
    public function quiz_group()
    {
        return $this->belongsTo(QuizGroup::class, 'group_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
