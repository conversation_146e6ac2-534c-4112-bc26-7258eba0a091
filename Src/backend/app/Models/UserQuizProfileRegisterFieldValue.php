<?php

namespace App\Models;

use App\Enums\RegisterConfigFieldTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserQuizProfileRegisterFieldValue extends Model
{
    use HasFactory;

    protected static function booted(): void
    {
        static::saving(function (UserQuizProfileRegisterFieldValue $userQuizProfileRegisterFieldValue) {
            $userQuizProfileRegisterFieldValue->quiz()->associate($userQuizProfileRegisterFieldValue->profile->user_quiz->quiz);

            if (!$userQuizProfileRegisterFieldValue->id) {
                if ($userQuizProfileRegisterFieldValue->field->type->is(RegisterConfigFieldTypeEnum::DefaultName)) {
                    $name = $userQuizProfileRegisterFieldValue->value;
                    $nameCount = 1;

                    while (UserQuizProfileRegisterFieldValue::where('value', $name)->where('quiz_id', $userQuizProfileRegisterFieldValue->quiz->id)->exists()) {
                        $name = $userQuizProfileRegisterFieldValue->value . ++$nameCount;
                    }

                    $userQuizProfileRegisterFieldValue->value = $name;
                }
            }
        });
    }

    /**
     * Relations.
     */
    public function profile()
    {
        return $this->belongsTo(UserQuizProfile::class, 'profile_id');
    }

    public function field()
    {
        return $this->belongsTo(RegisterConfigField::class, 'field_id');
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Accesors & Mutators.
     */
    // protected function value(): Attribute
    // {
    //     return Attribute::make(
    //         set: function ($value) {
    //             Log::info('set this', [$this]);

    //             if ($this->field->type->is(RegisterConfigFieldTypeEnum::DefaultName)) {
    //                 $name = $this->value;
    //                 $nameCount = 1;

    //                 while (UserQuizProfileRegisterFieldValue::where('value', $name)->where('quiz_id', $this->quiz->id)->exists()) {
    //                     $name = $this->value . ++$nameCount;
    //                 }

    //                 $value = $name;
    //             }

    //             return [
    //                 'value' => $value,
    //             ];
    //         }
    //     );
    // }
}
