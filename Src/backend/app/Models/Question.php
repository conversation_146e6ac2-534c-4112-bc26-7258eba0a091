<?php

namespace App\Models;

use App\Enums\LanguageEnum;
use App\Enums\QuestionStatusEnum;
use App\Enums\QuestionTypeEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Question extends Model implements HasMedia
{
    use HasFactory,
        InteractsWithMedia,
        SoftDeletes;

    protected $casts = [
        'status' => QuestionStatusEnum::class,
        'lang' => LanguageEnum::class,
        'type' => QuestionTypeEnum::class,
    ];

    protected $appends = [
        'image',
        'audio',
        'status_enum',
        'lang_enum',
        'type_enum',
    ];

    /**
     * Relations.
     */
    public function owner()
    {
        return $this->belongsTo(SystemUser::class, 'owner_id');
    }

    public function answers()
    {
        return $this->hasMany(Answer::class);
    }

    public function sets()
    {
        return $this->belongsToMany(QuestionSet::class, 'question_set_questions', 'question_id', 'set_id')
            ->withTimestamps()
            ;
    }

    public function theme_tags()
    {
        return $this->belongsToMany(ThemeTag::class, 'question_theme_tags')
            ->withTimestamps()
            ;
    }

    public function user_quiz_progress()
    {
        return $this->hasMany(UserQuizProgress::class);
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')->singleFile();
        $this->addMediaCollection('audio')->singleFile();
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function image(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('image'),
        );
    }

    protected function audio(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('audio'),
        );
    }



    /**
     * Accesors & Mutators.
     */
    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function langEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->lang ? $this->lang->value() : null,
        );
    }

    protected function typeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->type ? $this->type->value() : null,
        );
    }

    /**
     * Scopes
     */
    public function scopeWithSortedAnswers($query)
    {
        return $query->with(['answers' => function ($query) {
            $query->inRandomOrder();
        }]);
    }
}
