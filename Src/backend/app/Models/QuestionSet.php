<?php

namespace App\Models;

use App\Facades\Auth;
use App\Enums\ErrorEnum;
use App\Enums\QuestionSetStatusEnum;
use App\Enums\SystemUserTypeEnum;
use App\Models\Traits\QuestionSet\QuestionSetTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionSet extends Model
{
    use HasFactory,
        QuestionSetTrait;

    protected $table = 'questions_sets';

    protected $casts = [
        'is_public' => 'boolean',
        'status' => QuestionSetStatusEnum::class,
    ];

    protected $appends = [
        'status_enum',
    ];

    protected static function booted(): void
    {
        if (Auth::systemUser() && !Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            static::addGlobalScope('authOwnedBy', function (Builder $builder) {
                $builder->whereOwnerId(Auth::systemUser()->id)->orWhere('is_public', true);
            });
        }

        static::saved(function (QuestionSet $questionSet) {
            if ($questionSet->quizzes()->exists()) {
                $questionSet->reassignQuestionsQuizzes();
            }
        });

        static::deleting(function (QuestionSet $questionSet) {
            if ($questionSet->quizzes()->exists()) {
                ErrorEnum::QUIZ_SET_DELETE_NOT_ALLOWED_BECAUSE_HAS_QUIZZES_ERROR->throw();
            }

            foreach ($questionSet->questions as $question) {
                $question->forceDelete();
            }
        });
    }

    /**
     * Relations.
     */
    public function owner()
    {
        return $this->belongsTo(SystemUser::class, 'owner_id');
    }

    public function questions()
    {
        return $this->belongsToMany(Question::class, 'question_set_questions', 'set_id')
            ->withPivot('order')
            ->orderByPivot('order')
            ->withTimestamps()
            ;
    }

    public function theme_tags()
    {
        return $this->belongsToMany(ThemeTag::class, 'question_set_theme_tags', 'set_id')
            ->withTimestamps()
            ;
    }

    public function quizzes()
    {
        return $this->hasMany(Quiz::class);
    }

    public function quizzes_not_started()
    {
        return $this->quizzes()->where('starts_at', '>', Carbon::now());
    }

    /**
     * Accesors & Mutators.
     */
    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }
}
