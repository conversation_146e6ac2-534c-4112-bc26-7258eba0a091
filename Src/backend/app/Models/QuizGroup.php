<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizGroup extends Model
{
    use HasFactory;

    /**
     * Relations.
     */
    public function whitelist()
    {
        return $this->belongsTo(Whitelist::class);
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'quiz_group_users', 'group_id')->withTimestamps();
    }
}
