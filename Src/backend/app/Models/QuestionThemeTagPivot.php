<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class QuestionThemeTagPivot extends Pivot
{
    use HasFactory;

    protected $table = 'question_theme_tags';

    /**
     * Relations.
     */
    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function theme_tag()
    {
        return $this->belongsTo(ThemeTag::class);
    }
}
