<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class StyleConfig extends Model implements HasMedia
{
    use HasFactory,
        InteractsWithMedia;

    protected $appends = [
        'css_file',
    ];

    /**
     * Relations.
     */
    public function channel_quiz_configs()
    {
        return $this->hasMany(ChannelQuizConfig::class);
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('css_file')->singleFile();
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function cssFile(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('css_file'),
        );
    }
}
