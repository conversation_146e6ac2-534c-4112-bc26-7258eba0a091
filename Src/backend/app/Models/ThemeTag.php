<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ThemeTag extends Model
{
    use HasFactory;

    /**
     * Relations.
     */
    public function questions()
    {
        return $this->belongsToMany(Question::class, 'question_theme_tags')->withTimestamps();
    }

    public function sets()
    {
        return $this->belongsToMany(QuestionSet::class, 'question_set_theme_tags', 'set_id')->withTimestamps();
    }
}
