<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Answer extends Model
{
    use HasFactory;

    protected $casts = [
        'is_correct' => 'boolean',
    ];

    /**
     * Relations.
     */
    public function question()
    {
        return $this->belongsTo(Question::class);
    }

    public function user_quiz_progress()
    {
        return $this->hasMany(UserQuizProgress::class, 'selected_answer_id');
    }
}
