<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserQuiz extends Model
{
    use HasFactory;

    /**
     * Relations.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function profile()
    {
        return $this->hasOne(UserQuizProfile::class);
    }

    public function progress()
    {
        return $this->hasMany(UserQuizProgress::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function lastProgress(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->progress->sortBy('id')->last(),
        );
    }
}
