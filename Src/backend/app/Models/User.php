<?php

namespace App\Models;

use App\Enums\LanguageEnum;
use App\Enums\UserStatusEnum;
use App\Models\Traits\Auth\AuthTrait;
use App\Models\Traits\User\UserTrait;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail, HasMedia
{
    use HasApiTokens,
        HasRoles,
        HasFactory,
        Notifiable,
        SoftDeletes,
        InteractsWithMedia,
        UserTrait,
        AuthTrait;

    protected $hidden = [
        'media',
        'password',
        'token_for_email_verification',
        'token_for_password_reset',
    ];

    protected $dates = [
        'email_verified_at'
    ];

    protected $casts = [
        'status' => UserStatusEnum::class,
        'lang' => LanguageEnum::class,
    ];

    protected $appends = [
        'full_name',
        'picture',
        'status_enum',
        'lang_enum',
    ];

    /**
     * Relations.
     */
    public function quizzes()
    {
        return $this->belongsToMany(Quiz::class, 'user_quizzes')->using(UserQuizPivot::class)->withPivot('id')->withTimestamps();
    }

    public function user_quizzes()
    {
        return $this->hasMany(UserQuiz::class, 'user_id');
    }

    public function profiles()
    {
        return $this->hasManyThrough(UserQuizProfile::class, UserQuiz::class);
    }

    public function profile_on_quiz(int $quizId)
    {
        return $this->hasOneThrough(UserQuizProfile::class, UserQuiz::class)->where('user_quizzes.quiz_id', $quizId);
    }

    public function progress()
    {
        return $this->hasManyThrough(UserQuizProgress::class, UserQuiz::class);
    }

    public function progress_on_quiz(int $quizId)
    {
        return $this->progress()->where('user_quizzes.quiz_id', $quizId)->orderBy('user_quiz_progress.id');
    }

    public function groups()
    {
        return $this->belongsToMany(QuizGroup::class, 'quiz_group_users', 'user_id', 'group_id')->withTimestamps();
    }

    public function group_on_quiz(int $quizId)
    {
        return $this->groups()->where('quiz_id', $quizId);
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('picture');
    }

    /**
     * Accesors & Mutators.
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, array $attributes) => trim($attributes['first_name'] . ' ' . $attributes['last_name']),
        );
    }

    protected function picture(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMediaUrl('picture'),
        );
    }

    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function langEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->lang ? $this->lang->value() : null,
        );
    }
}
