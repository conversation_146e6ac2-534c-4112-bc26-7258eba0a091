<?php

namespace App\Models;

use App\Enums\ChannelQuizConfigAskForEmailEnum;
use App\Enums\ChannelQuizConfigScoringModeEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChannelQuizConfig extends Model
{
    use HasFactory;

    protected $casts = [
        'allow_lose_points' => 'boolean',
        'allow_negative_points' => 'boolean',
        'use_random_questions' => 'boolean',
        'show_ranking_between_questions' => 'boolean',
        'show_correct_answer' => 'boolean',
        'play_audio_on_monitor' => 'boolean',
        'ask_for_email_at_the_end' => ChannelQuizConfigAskForEmailEnum::class,
        'scoring_mode' => ChannelQuizConfigScoringModeEnum::class,
        'show_ranking_upon_completion' => 'boolean'
    ];

    protected $appends = [
        'ask_for_email_enum',
        'scoring_mode_enum'
    ];

    /**
     * Relations.
     */
    public function multimedia_config()
    {
        return $this->belongsTo(MultimediaConfig::class);
    }

    public function style_config()
    {
        return $this->belongsTo(StyleConfig::class);
    }

    public function prize_configs()
    {
        return $this->hasMany(PrizeConfig::class, 'config_id');
    }

    public function register_config_fields()
    {
        return $this->hasMany(RegisterConfigField::class, 'config_id');
    }

    public function whitelist()
    {
        return $this->belongsTo(Whitelist::class);
    }

    public function quiz()
    {
        return $this->hasOne(Quiz::class, 'config_id');
    }

    public function channel()
    {
        return $this->hasOne(Quiz::class, 'config_id');
    }


    /**
     * Accesors & Mutators.
     */
    protected function askForEmailEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->ask_for_email_at_the_end ? $this->ask_for_email_at_the_end->value() : null,
        );
    }

    protected function scoringModeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->scoring_mode ? $this->scoring_mode->value() : null,
        );
    }
    
}
