<?php

namespace App\Models;

use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Enums\GiveawayStatusEnum;
use App\Models\Traits\Giveaway\GiveawayTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Giveaway extends Authenticatable implements HasMedia
{
    use HasApiTokens;
    use HasFactory;
    use InteractsWithMedia;
    use GiveawayTrait;

    protected $casts = [
        'allowed_participants_mode' => GiveawayAllowedParticipantsModeEnum::class,
        'status' => GiveawayStatusEnum::class,
        'participants' => 'array',
        'winners' => 'array',
        'black_list' => 'array',
    ];

    protected $appends = [
        'allowed_participants_mode_enum',
        'status_enum',
        'logo',
        'bg_video',
        'monitor_bg_video',
        'monitor_url',
        'winners_count',
    ];

    protected $with = [
        'prizes'
    ];

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')->singleFile();
        $this->addMediaCollection('bg_video')->singleFile();
    }

    /**
     * Relations.
     */
    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function prizes()
    {
        return $this->hasMany(GiveawayPrize::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function allowedParticipantsModeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->allowed_participants_mode ? $this->allowed_participants_mode->value() : null,
        );
    }

    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function logo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('logo'),
        );
    }

    protected function bgVideo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('bg_video'),
        );
    }

    protected function monitorBgVideo(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('monitor_bg_video'),
        );
    }

    protected function monitorUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_monitor_url') . '/sorteo/' . $this->id,
        );
    }

    protected function winnersCount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->winners ? count($this->winners) : null,
        );
    }
}
