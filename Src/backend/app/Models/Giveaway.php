<?php

namespace App\Models;

use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Enums\GiveawayStatusEnum;
use App\Models\Traits\Giveaway\GiveawayTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Giveaway extends Authenticatable implements HasMedia
{
    use HasApiTokens;
    use HasFactory;
    use GiveawayTrait;
    use InteractsWithMedia;

    protected $casts = [
        'allowed_participants_mode' => GiveawayAllowedParticipantsModeEnum::class,
        'status' => GiveawayStatusEnum::class,
        'participants' => 'array',
        'winners' => 'array',
        'black_list' => 'array',
        'audio_enabled' => 'boolean',
    ];

    protected $appends = [
        'allowed_participants_mode_enum',
        'status_enum',
        'monitor_url',
        'winners_count',
        'revealed_winners_count',
        'participants_excel',
        'participants_raw',
    ];

    protected $with = [
        'prizes',
        'quiz',
        'channel',
        'style_config',
        'multimedia_config',
    ];

    /**
     * Relations.
     */
    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }

    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    public function prizes()
    {
        return $this->hasMany(GiveawayPrize::class);
    }

    public function style_config()
    {
        return $this->belongsTo(StyleConfig::class);
    }

    public function multimedia_config()
    {
        return $this->belongsTo(MultimediaConfig::class);
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('participants_excel')->singleFile();
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function participantsExcel(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('participants_excel'),
        );
    }

    /**
     * Accesors & Mutators.
     */
    protected function allowedParticipantsModeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->allowed_participants_mode ? $this->allowed_participants_mode->value() : null,
        );
    }

    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function monitorUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => config('app.webapp_monitor_url') . '/sorteo/' . $this->id,
        );
    }

    protected function winnersCount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->winners ? count($this->winners) : null,
        );
    }

    protected function revealedWinnersCount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->winners ? collect($this->winners)->filter(fn ($winner) => $winner['revealed'])->count() : null,
        );
    }

    protected function participants(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $attributes['black_list']
                ? collect($value)
                    ->filter(fn ($participant) => !in_array($participant['id'], $attributes['black_list']))
                : $value,
        );
    }

    protected function participantsRaw(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => $attributes['participants'],
        );
    }
}
