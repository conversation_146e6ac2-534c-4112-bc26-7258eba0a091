<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class QuestionSetQuestionPivot extends Pivot
{
    use HasFactory;

    protected $table = 'question_set_questions';

    /**
     * Relations.
     */
    public function question_set()
    {
        return $this->belongsTo(QuestionSet::class, 'set_id');
    }

    public function question()
    {
        return $this->belongsTo(Question::class);
    }
}
