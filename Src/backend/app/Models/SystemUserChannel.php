<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemUserChannelPivot extends Model
{
    use HasFactory;

    protected $table = 'system_user_channels';

    /**
     * Relations.
     */
    public function system_user()
    {
        return $this->belongsTo(SystemUser::class);
    }

    public function channel()
    {
        return $this->belongsTo(Channel::class);
    }
}
