<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class QuestionSetThemeTagPivot extends Pivot
{
    use HasFactory;

    protected $table = 'question_set_theme_tags';

    /**
     * Relations.
     */
    public function set()
    {
        return $this->belongsTo(QuestionSet::class, 'set_id');
    }

    public function theme_tag()
    {
        return $this->belongsTo(ThemeTag::class);
    }
}
