<?php

namespace App\Models;

use App\Enums\RegisterConfigFieldTypeEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class RegisterConfigField extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $casts = [
        'validations' => 'array',
        'type' => RegisterConfigFieldTypeEnum::class,
        'show_in_ranking' => 'boolean',
    ];

    protected $appends = [
        'type_enum',
    ];

    /**
     * Relations.
     */
    public function config()
    {
        return $this->belongsTo(ChannelQuizConfig::class, 'config_id');
    }

    public function user_quiz_profile_register_field_values()
    {
        return $this->hasMany(UserQuizProfileRegisterFieldValue::class);
    }


    /**
     * Accesors & Mutators.
     */
    protected function typeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->type ? $this->type->value() : null,
        );
    }

    protected function name(): Attribute
    {
        return Attribute::make(
            set: fn (string $value) => Str::snake($value),
        );
    }
}
