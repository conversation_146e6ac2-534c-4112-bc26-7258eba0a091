<?php

namespace App\Models;

use App\Facades\Auth;
use App\Enums\LanguageEnum;
use App\Enums\SocialDriverEnum;
use App\Enums\SystemUserStatusEnum;
use App\Enums\SystemUserTypeEnum;
use App\Models\Traits\Auth\AuthTrait;
use App\Models\Traits\SystemUser\SystemUserTrait;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;

class SystemUser extends Authenticatable implements MustVerifyEmail, HasMedia
{
    use <PERSON><PERSON><PERSON>Tokens,
        HasR<PERSON><PERSON>,
        HasFactory,
        Notifiable,
        SoftDeletes,
        InteractsWithMedia,
        SystemUserTrait,
        AuthTrait;

    protected $guard_name = 'system_user';

    protected $hidden = [
        'media',
        'password',
        'token_for_email_verification',
        'token_for_password_reset',
        'roles',
        'permissions',
    ];

    protected $dates = [
        'email_verified_at'
    ];

    protected $casts = [
        'social_driver' => SocialDriverEnum::class,
        'type' => SystemUserTypeEnum::class,
        'status' => SystemUserStatusEnum::class,
        'lang' => LanguageEnum::class,
    ];

    protected $appends = [
        'full_name',
        'social_driver_enum',
        'type_enum',
        'status_enum',
        'lang_enum',
        'roles_and_permissions',
        'has_active_channels',
        'picture',
        'photos',
    ];

    protected $with = [
        'parent',
    ];

    protected static function booted(): void
    {
        if (Auth::systemUser() && !Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            static::addGlobalScope('authOwnedBy', function (Builder $builder) {
                $builder->childrenOf(Auth::systemUser()->id);
            });
        }
    }

    /**
     * Relations.
     */
    public function parent()
    {
        return $this->belongsTo(SystemUser::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(SystemUser::class, 'parent_id');
    }

    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    public function questions_sets()
    {
        return $this->hasMany(QuestionSet::class);
    }

    public function channel()
    {
        return $this->hasOne(Channel::class);
    }

    public function channels()
    {
        return $this->hasMany(Channel::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, array $attributes) => trim($attributes['first_name'] . ' ' . $attributes['last_name']),
        );
    }

    protected function socialDriverEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->social_driver ? $this->social_driver->value() : null,
        );
    }

    protected function typeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->type ? $this->type->value() : null,
        );
    }

    protected function statusEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->status ? $this->status->value() : null,
        );
    }

    protected function langEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->lang ? $this->lang->value() : null,
        );
    }

    protected function rolesAndPermissions(): Attribute
    {
        return Attribute::make(
            get: function () {
                return [
                    'roles' => $this->getRoleNames(),
                    'permissions' => $this->getAllPermissions()->map(fn ($item) => $item['name']),
                ];
            }
        );
    }

    protected function hasActiveChannels(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->channels()->whereStatus('Active')->exists(),
        );
    }

    /**
     * Media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('picture')->singleFile();
        $this->addMediaCollection('photos');
    }

    /**
     * Accesors & Mutators (for media collections).
     */
    protected function picture(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getFirstMedia('picture'),
        );
    }

    protected function photos(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getMedia('photos'),
        );
    }

    /**
     * Scopes.
     */
    public function scopeChildrenOf(Builder $query, int $id) {
        return $query->whereParentId($id);
    }

    public function scopeNoRoot(Builder $query) {
        return $query->where('id', '!=', 1);
    }
}
