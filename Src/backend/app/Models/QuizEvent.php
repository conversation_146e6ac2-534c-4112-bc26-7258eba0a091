<?php

namespace App\Models;

use App\Enums\QuizEventTypeEnum;
use App\Websockets\QuizChangedSocket;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizEvent extends Model
{
    use HasFactory;

    protected $casts = [
        'type' => QuizEventTypeEnum::class,
    ];

    protected $appends = [
        'type_enum',
    ];

    protected static function booted(): void
    {
        static::saved(function (QuizEvent $quizEvent) {
            // QuizChangedSocket::dispatch($quizEvent->quiz->id);
        });

        static::deleted(function (QuizEvent $quizEvent) {
            // QuizChangedSocket::dispatch($quizEvent->quiz->id);
        });
    }

    /**
     * Relations.
     */
    public function quiz()
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Accesors & Mutators.
     */
    protected function typeEnum(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->type ? $this->type->value() : null,
        );
    }
}
