<?php

namespace App\Models;

use App\Facades\Auth;
use App\Enums\RegisterConfigFieldTypeEnum;
use App\Enums\SystemUserTypeEnum;
use App\Models\Traits\UserQuizProfile\UserQuizProfileTrait;
use App\Websockets\QuizUsersCountChangedSocket;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserQuizProfile extends Model
{
    use HasFactory,
        UserQuizProfileTrait;

    protected $casts = [
        'question_ids' => 'array',
    ];

    protected static function booted(): void
    {
        if (Auth::systemUser() && !Auth::systemUser()->type->is(SystemUserTypeEnum::Administrator)) {
            static::addGlobalScope('authOwnedBy', function (Builder $builder) {
                $builder->whereHas('user_quiz.quiz.channel', function ($query) {
                    return $query->whereSystemUserId(Auth::systemUser()->id);
                });
            });
        }

        static::saved(function (UserQuizProfile $userQuizProfile) {
            // QuizUsersCountChangedSocket::dispatch($userQuizProfile->user_quiz->quiz);
        });
    }

    /**
     * Relations.
     */
    public function user_quiz()
    {
        return $this->belongsTo(UserQuiz::class);
    }

    public function register_field_values()
    {
        return $this->hasMany(UserQuizProfileRegisterFieldValue::class, 'profile_id');
    }

    /**
     * Accesors & Mutators.
     */
    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->register_field_values->firstWhere('field.type', RegisterConfigFieldTypeEnum::DefaultName)->value ?? null,
        );
    }

    protected function email(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->register_field_values->firstWhere('field.type', RegisterConfigFieldTypeEnum::DefaultEmail)->value ?? null,
        );
    }

    protected function dni(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->register_field_values->firstWhere('field.type', RegisterConfigFieldTypeEnum::DefaultDNI)->value ?? null,
        );
    }

    protected function extraRegisterDataForRanking(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->register_field_values->where('field.show_in_ranking', 1)->load('field')->map(function ($item) {
                return [
                    'value' => $item->value,
                    'label' => $item->field->label_for_ranking
                ];
            })->values() ?? null,
        );
    }

    protected function group(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->user_quiz->user->groups()
                ->where('quiz_id', $this->user_quiz->quiz->id)
                ->first() ?? null
        );
    }

    public function scopeHasQuiz($query)
    {
        return $query->whereHas('user_quiz', function ($query) {
            return $query->has('quiz');
        });
    }
}
