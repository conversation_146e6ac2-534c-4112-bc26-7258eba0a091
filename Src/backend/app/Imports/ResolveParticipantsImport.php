<?php

namespace App\Imports;

use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ResolveParticipantsImport extends BaseImport implements WithHeadingRow
{
    protected function _columns($row): array
    {
        return $row->toArray();
    }

    protected function _rules(): array
    {
        return [];
    }

    protected function _data($row, $key): array
    {
        return [
            'id' => (string) ($key + 1),
            'name' => $row ? ucwords(strtolower(reset($row))) : null,
            'custom_fields' => collect($row)->map(function ($value, $label) {
                return [
                    'value' => $value,
                    'label' => ucfirst($label),
                ];
            })->values()->all(),
        ];
    }
}
