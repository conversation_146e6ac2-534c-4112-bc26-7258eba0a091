<?php

namespace App\Imports;

use App\Enums\QuestionTypeEnum;
use Illuminate\Support\Str;

class QuestionImport extends BaseImport
{
    protected function _columns($row): array
    {
        return [
            'pregunta' => $row[0] ?? null,
            'respuesta_correcta' => $row[1] ?? null,
            'respuesta_incorrecta_1' => $row[2] ?? null,
            'respuesta_incorrecta_2' => $row[3] ?? null,
            'tipo' => $row[4] ?? null,
            'url_de_imagen' => $row[5] ?? null,
            'url_de_audio' => $row[6] ?? null,
        ];
    }

    protected function _rules(): array
    {
        return [
            '*.pregunta' => ['bail', 'required', 'max:120'],
            '*.respuesta_correcta' => ['bail', 'required'],
            '*.respuesta_incorrecta_1' => ['bail', 'required'],
            '*.respuesta_incorrecta_2' => ['bail', 'required'],
            '*.tipo' => ['bail', 'required', 'in:normal,doble,bomba,encuesta,Normal,Doble,Bomba,Encuesta'],
            '*.url_de_imagen' => ['bail', 'nullable', 'url'],
            '*.url_de_audio' => ['bail', 'nullable', 'url'],
        ];
    }

    protected function _data($row, $key): array
    {
        switch (Str::lower($row['tipo'])) {
            case 'normal': $type = QuestionTypeEnum::Normal->value; break;
            case 'doble': $type = QuestionTypeEnum::Double->value; break;
            case 'bomba': $type = QuestionTypeEnum::Bomb->value; break;
            case 'encuesta': $type = QuestionTypeEnum::Survey->value; break;
        }

        return [
            'body' => $row['pregunta'],
            'is_public' => false,
            'type' => $type,
            'image_url' => $row['url_de_imagen'],
            'audio_url' => $row['url_de_audio'],
            'answers' => [
                ['body' => $row['respuesta_correcta'], 'is_correct' => true],
                ['body' => $row['respuesta_incorrecta_1'], 'is_correct' => $type === QuestionTypeEnum::Survey->value ? true : false],
                ['body' => $row['respuesta_incorrecta_2'], 'is_correct' => $type === QuestionTypeEnum::Survey->value ? true : false],
            ],
            'pivot' => ['order' => $key + 1],
        ];
    }
}
