<?php

namespace App\Imports;

use App\Core\Response\ErrorException;
use App\Models\Media;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

abstract class BaseImport implements WithHeadingRow
{
    use Importable;

    private Media $_media;
    private bool $_deleteMedia;
    protected Collection $_data;

    public function __construct(int $mediaId, bool $deleteMedia = false, bool $preserveHeadings = false)
    {
        $this->_media = Media::findOrFail($mediaId);
        $this->_deleteMedia = $deleteMedia;

        $this->_data = $this->toCollection($this->_media->getPathRelativeToRoot(), $this->_media->disk)
            ->map(function ($rows) use ($preserveHeadings) {
                return collect($rows)->map(function ($row) use ($preserveHeadings) {
                    return $this->_columns(
                        $preserveHeadings ? collect($row) : collect($row)->flatten()
                    );
                });
            })
            ->flatten(1);
    }

    /* -------------------- */

    abstract protected function _columns($columns): array;
    abstract protected function _rules(): array;
    abstract protected function _data($row, $key): array;

    /* -------------------- */

    public function getData(): array
    {
        if (!empty($rules = $this->_rules())) {
            $validator = Validator::make($this->_data->toArray(), $rules);

            if ($validator->fails()) {
                $errorsToTake = 10;
                $errorsCount = collect($validator->errors())->undot()->count();
                $otherErrorsCount = $errorsCount - $errorsToTake;
                $otherErrorsCount = $otherErrorsCount < 0 ? 0 : $otherErrorsCount;

                throw new ErrorException(
                    body: [
                        'file_validation' => [
                            'rows_with_errors' => $errorsCount,
                            'other_rows_with_errors' => $otherErrorsCount,
                            'rows' => collect($validator->errors())->undot()->take($errorsToTake)->map(function (array $item, int $key) {
                                return [
                                    'row' => $key + 2,
                                    'columns' => collect($item)->map(function (array $column, string $key) {
                                        return [
                                            'name' => $key,
                                            'errors' => $column,
                                        ];
                                    })
                                    ->values(),
                                ];
                            })
                            ->sortKeys()
                            ->values()
                        ],
                    ],
                    message: 'Algunas de las filas del archivo contienen errores.',
                    code: 422,
                );
            }
        }

        $data = $this->_data->map(function ($row, $key) {
            return $this->_data($row, $key);
        })->toArray();

        if ($this->_deleteMedia) {
            $this->_media->delete();
        }

        return $data;
    }
}
