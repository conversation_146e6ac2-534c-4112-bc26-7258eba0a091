<?php

namespace App\UseCases\Giveaway\ResolveParticipants;

use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Models\Media;

class ResolveParticipantsDTO
{
    public function __construct(
        public ?int $fileId = null,
        public ?int $quizId = null,
        public ?GiveawayAllowedParticipantsModeEnum $allowedParticipantsMode = null,
        public ?int $minCorrectAnswers = null,
        public ?int $maxTopRanking = null,
        public ?array $blackList = [],
    ) {
    }
}


