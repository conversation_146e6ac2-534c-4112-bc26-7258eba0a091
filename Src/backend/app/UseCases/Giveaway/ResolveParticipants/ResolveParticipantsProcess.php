<?php

namespace App\UseCases\Giveaway\ResolveParticipants;

use App\DynamoDbModels\QuizRanking;
use App\Enums\GiveawayAllowedParticipantsModeEnum;
use App\Imports\ResolveParticipantsImport;

class ResolveParticipantsProcess
{
    public function run(ResolveParticipantsDTO $dto): array
    {
        if ($dto->fileId) {
            $participants = $this->_resolveParticipantsFromFile($dto);
        } else {
            $participants = $this->_resolveParticipantsFromQuiz($dto);
        }

        return $participants;
    }

    private function _resolveParticipantsFromFile(ResolveParticipantsDTO $dto): array
    {
        $participants = (new ResolveParticipantsImport($dto->fileId, false, true))->getData();

        if ($dto->blackList) {
            $participants = collect($participants)
                ->filter(fn ($participant) => !in_array($participant['id'], $dto->blackList))
                ->values()
                ->all();
        }

        return $participants;
    }

    private function _resolveParticipantsFromQuiz(ResolveParticipantsDTO $dto): array
    {
        $quizRanking = QuizRanking::where('quiz_id', $dto->quizId)->first();
        $participants = collect($quizRanking->items ?? []);

        $participants = match ($dto->allowedParticipantsMode) {
            GiveawayAllowedParticipantsModeEnum::All => $participants,
            GiveawayAllowedParticipantsModeEnum::NonWinners => $participants->filter(fn ($participant) => $participant['position'] > $participant['podium']),
            GiveawayAllowedParticipantsModeEnum::CorrectAnswers => $participants->filter(fn ($participant) => $participant['points'] >= $dto->minCorrectAnswers),
            GiveawayAllowedParticipantsModeEnum::TopRanking => $participants->take($dto->maxTopRanking),
            default => $participants,
        };

        if (!empty($participants)) {
            $participants = $participants->map(function ($participant) {
                return [
                    'id' => $participant['user_profile_id'],
                    'name' => ucwords(strtolower($participant['name'])),
                    'email' => $participant['email'],
                    'custom_fields' => $participant['custom_fields'],
                    'position' => $participant['position'],
                    'points' => $participant['points'],
                ];
            })->sortBy('name')
                ->values()
                ->all();
        }

        if ($dto->blackList) {
            $participants = collect($participants)
                ->filter(fn ($participant) => !in_array($participant['id'], $dto->blackList))
                ->values()
                ->all();
        }

        return $participants;
    }
}


