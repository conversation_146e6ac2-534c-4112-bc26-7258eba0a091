<?php

namespace App\UseCases\Giveaway\ResolveParticipants;

use App\Models\Giveaway;

class ResolveParticipants
{
    public function __construct(
        private ResolveParticipantsProcess $resolveParticipantsProcess,
    ) {
    }

    public function run(Giveaway $giveaway): Giveaway
    {
        $giveaway->participants = $this->resolveParticipantsProcess->run(new ResolveParticipantsDTO(
            fileId: $giveaway->participants_excel?->id,
            quizId: $giveaway->quiz?->id,
            allowedParticipantsMode: $giveaway->allowed_participants_mode,
            minCorrectAnswers: $giveaway->min_correct_answers,
            maxTopRanking: $giveaway->max_top_ranking,
            blackList: $giveaway->black_list,
        ));

        $giveaway->saveOrFail();

        return $giveaway;
    }
}


