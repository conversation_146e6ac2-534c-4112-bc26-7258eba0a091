<?php

namespace App\UseCases\Giveaway\ResolveWinners;

use App\Enums\GiveawayStatusEnum;

class ResolveWinnersProcess
{
    public function run(ResolveWinnersDTO $dto)
    {
        if ($dto->giveaway->status !== GiveawayStatusEnum::Pending) {
            return;
        }

        $participants = collect($dto->giveaway->participants);
        $winners = [];

        foreach ($dto->giveaway->prizes as $prize) {
            $participants = $participants->filter(function ($participant) use ($winners) {
                return !collect($winners)->firstWhere('id', $participant['id']);
            });

            $prizeWinners = $participants->shuffle()->take($prize->quantity)
                ->map(function ($winner) use ($prize) {
                    return [
                        'prize_id' => $prize->id,
                        'prize_name' => $prize->name,
                        'id' => $winner['id'],
                        'name' => $winner['name'],
                        'email' => $winner['email'] ?? null,
                        'custom_fields' => $winner['custom_fields'],
                        'position' => $winner['position'] ?? null,
                        'points' => $winner['points'] ?? null,
                        'revealed' => false,
                    ];
                })->values()->all();

            $winners = array_merge($winners, $prizeWinners);
        }

        return $winners;
    }
}


