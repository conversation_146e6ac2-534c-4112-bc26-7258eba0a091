<?php

namespace App\UseCases\Giveaway\ResolveWinners;

use App\Models\Giveaway;

class ResolveWinners
{
    public function __construct(
        private ResolveWinnersProcess $resolveWinnersProcess,
    ) {
    }

    public function run(Giveaway $giveaway): Giveaway
    {
        $giveaway->winners = $this->resolveWinnersProcess->run(new ResolveWinnersDTO($giveaway));
        $giveaway->saveOrFail();

        return $giveaway;
    }
}


