<?php

namespace App\Facades;

use App\Models\AppClient;
use App\Models\Giveaway;
use App\Models\Quiz;
use App\Models\SystemUser;
use App\Models\User;
use Illuminate\Support\Facades\Auth as AuthFacade;

class Auth extends AuthFacade
{
    public static function getAppClientGuardName(): string
    {
        return 'app_client';
    }

    public static function getSystemUserGuardName(): string
    {
        return 'system_user';
    }

    public static function getUserGuardName(): string
    {
        return 'user';
    }

    public static function getQuizGuardName(): string
    {
        return 'quiz';
    }

    public static function getGiveawayGuardName(): string
    {
        return 'giveaway';
    }

    /* -------------------- */

    public static function appClientGuard(): \Illuminate\Contracts\Auth\Guard|\Illuminate\Contracts\Auth\StatefulGuard
    {
        return static::guard(self::getAppClientGuardName());
    }

    public static function systemUserGuard(): \Illuminate\Contracts\Auth\Guard|\Illuminate\Contracts\Auth\StatefulGuard
    {
        return static::guard(self::getSystemUserGuardName());
    }

    public static function userGuard(): \Illuminate\Contracts\Auth\Guard|\Illuminate\Contracts\Auth\StatefulGuard
    {
        return static::guard(self::getUserGuardName());
    }

    public static function quizGuard(): \Illuminate\Contracts\Auth\Guard|\Illuminate\Contracts\Auth\StatefulGuard
    {
        return static::guard(self::getQuizGuardName());
    }

    public static function giveawayGuard(): \Illuminate\Contracts\Auth\Guard|\Illuminate\Contracts\Auth\StatefulGuard
    {
        return static::guard(self::getGiveawayGuardName());
    }

    /* -------------------- */

    public static function appClient(): ?AppClient
    {
        return self::appClientGuard()->user();
    }

    public static function systemUser(): ?SystemUser
    {
        return self::systemUserGuard()->user();
    }

    public static function user(): ?User
    {
        return self::userGuard()->user();
    }

    public static function quiz(): ?Quiz
    {
        return self::quizGuard()->user();
    }

    public static function giveaway(): ?Giveaway
    {
        return self::giveawayGuard()->user();
    }

    /* -------------------- */

    public static function verifyAppClient(?AppClient $appClient = null): void
    {
        if (!$appClient) {
            $appClient = self::appClient();
        }

        if ($appClient) {
            $appClient->verifyStatus();
            $appClient->verifyHost();
            $appClient->verifyScope();
        }
    }

    public static function verifySystemUser(?SystemUser $systemUser = null): void
    {
        if (!$systemUser) {
            $systemUser = self::systemUser();
        }

        if ($systemUser) {
            if ($token = $systemUser->currentAccessToken()) {
                $tokenName = explode('|', $token->name);

                if ($tokenName[0] === 'app_client' && $appClientId = $tokenName[1] ?? null) {
                    self::verifyAppClient(AppClient::findOrFail($appClientId));
                }
            }

            $systemUser->verifyStatus();
        }
    }

    public static function verifyUser(?User $user = null): void
    {
        if (!$user) {
            $user = self::user();
        }

        if ($user) {
            // if ($token = $user->currentAccessToken()) {
            //     $tokenName = explode('|', $token->name);

            //     if ($tokenName[0] === 'app_client' && $appClientId = $tokenName[1] ?? null) {
            //         self::verifyAppClient(AppClient::findOrFail($appClientId));
            //     }
            // }

            $user->verifyStatus();
        }
    }

    public static function verifyQuiz(?Quiz $quiz = null): void
    {
        if (!$quiz) {
            $quiz = self::user();
        }

        if ($quiz) {
            $quiz->verifyStatus();
        }
    }

    public static function verifyGiveaway(?Giveaway $giveaway = null): void
    {
        if (!$giveaway) {
            $giveaway = self::user();
        }

        if ($giveaway) {
            $giveaway->verifyStatus();
        }
    }
}
