<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\UserStatusEnum;
use App\Models\User;

class UserRepository extends BaseRepository
{
    public static function save(array $input, ?User $user = null): User
    {
        $isNew = !$user;

        if ($isNew) {
            $user = new User();
            $user->status = UserStatusEnum::Active;
        }

        static::_fill([
            'first_name',
            'last_name',
            'email',
            'password',
            'social_id',
            'social_driver',
            'social_avatar',
            'status',
        ], $input, $user);

        $user->saveOrFail();

        static::_fillMedia([
            'picture',
        ], $input, $user);

        $user->refresh();

        return $user;
    }
}
