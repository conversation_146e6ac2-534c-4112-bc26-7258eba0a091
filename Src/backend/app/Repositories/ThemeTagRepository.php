<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\ThemeTag;

class ThemeTagRepository extends BaseRepository
{
    public static function save(array $input, ?ThemeTag $themeTag = null): ThemeTag
    {
        $isNew = false;

        if (!$themeTag) {
            $themeTag = new ThemeTag();

            $isNew = true;
        }

        static::_fill([
            'name',
        ], $input, $themeTag);

        $themeTag->saveOrFail();
        $themeTag->refresh();

        return $themeTag;
    }
}
