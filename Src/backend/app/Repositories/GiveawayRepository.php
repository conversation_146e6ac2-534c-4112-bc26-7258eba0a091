<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\Giveaway;
use App\Models\GiveawayPrize;

class GiveawayRepository extends BaseRepository
{
    public static function save(array $input, ?Giveaway $giveaway = null): Giveaway
    {
        $isNew = false;

        if (!$giveaway) {
            $giveaway = new Giveaway();

            $isNew = true;
        }

        static::_fill([
            'name',
            'status',
            'allowed_participants_mode',
            'min_correct_answers',
            'color_primary',
            'audio_enabled',
            'draw_duration_seconds',
            'participants',
            'winners',
            'black_list',
        ], $input, $giveaway);

        static::_fillBelongs([
            'quiz',
        ], $input, $giveaway);

        $giveaway->saveOrFail();

        static::_fillMedia([
            'logo',
            'bg_video',
            'monitor_bg_video',
        ], $input, $giveaway);

        static::_fillManies([
            'prizes' => [GiveawayPrizeRepository::class, GiveawayPrize::class, 'giveaway', true],
        ], $input, $giveaway);

        if ($isNew) {
            $giveaway->generatePin();
        }

        $giveaway->refresh();

        return $giveaway;
    }
}
