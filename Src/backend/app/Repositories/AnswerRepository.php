<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\Answer;

class AnswerRepository extends BaseRepository
{
    public static function save(array $input, ?Answer $answer = null): Answer
    {
        $isNew = false;

        if (!$answer) {
            $answer = new Answer();

            $isNew = true;
        }

        static::_fill([
            'body',
            'is_correct',
        ], $input, $answer);

        static::_fillBelongs([
            'question',
        ], $input, $answer);

        $answer->saveOrFail();
        $answer->refresh();

        return $answer;
    }
}
