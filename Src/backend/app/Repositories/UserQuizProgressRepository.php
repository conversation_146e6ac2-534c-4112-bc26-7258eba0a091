<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Data\UserQuizProgressData;
use App\DynamoDbModels\UserQuizProgress;
use Illuminate\Support\Str;

class UserQuizProgressRepository extends BaseRepository
{
    public static function save(array $input, ?UserQuizProgress $userQuizProgress = null): UserQuizProgress
    {
        $isNew = !$userQuizProgress;

        if ($isNew) {
            $userQuizProgress = new UserQuizProgress();
        }

        if (array_key_exists('selected_answer', $input)) {
            $input['selected_answer'] = collect($input['selected_answer'])->mapWithKeys(fn ($item, $key) => [Str::snake($key) => $item])->all();
        }

        $data = UserQuizProgressData::from($input);

        static::_fill([
            'id',
            'quiz_id',
            'user_id',
            'step_id',
            'quiz_type',
            'question_id',
            'selected_answer_id',
            'selected_answer_is_correct',
            'points_multiplier',
            'allow_lose_points',
            'allow_negative_points',
            'points_before',
            'points_earned',
            'has_websocket_connection',
        ], $data->all(), $userQuizProgress);

        $userQuizProgress->saveOrFail();
        $userQuizProgress->refresh();

        return $userQuizProgress;
    }
}
