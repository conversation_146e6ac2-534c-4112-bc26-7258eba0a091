<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Data\UserQuizProfileData;
use App\DynamoDbModels\UserQuizProfile;

class UserQuizProfileRepository extends BaseRepository
{
    public static function save(array $input, ?UserQuizProfile $userQuizProfile = null): UserQuizProfile
    {
        $isNew = !$userQuizProfile;

        if ($isNew) {
            $userQuizProfile = new UserQuizProfile();
        }

        $data = UserQuizProfileData::from($input);

        static::_fill([
            'id',
            'user_id',
            'quiz_id',
            'quiz_name',
            'channel_id',
            'channel_name',
            'group_id',
            'register_field_values',
            'question_ids',
            'current_step_id',
            'end_email',
            'name',
            'email',
            'dni',
            'has_websocket_connection',
        ], $data->all(), $userQuizProfile);

        $userQuizProfile->saveOrFail();
        $userQuizProfile->refresh();

        return $userQuizProfile;
    }
}
