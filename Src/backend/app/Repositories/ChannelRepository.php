<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\ChannelStatusEnum;
use App\Models\Channel;

class ChannelRepository extends BaseRepository
{
    public static function save(array $input, ?Channel $channel = null): Channel
    {
        $isNew = false;

        if (!$channel) {
            $channel = new Channel();
            $channel->status = ChannelStatusEnum::Active;

            $isNew = true;
        }

        static::_fill([
            'name',
            'slug',
            'website_url',
            'tyc_url',
            'email',
            'status',
            'is_protected',
        ], $input, $channel);

        static::_fillBelongs([
            'config' => ChannelQuizConfigRepository::class,
            'system_user',
        ], $input, $channel);

        $channel->saveOrFail();
        $channel->refresh();

        return $channel;
    }
}
