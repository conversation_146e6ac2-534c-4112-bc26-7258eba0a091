<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\GiveawayPrize;
use App\Models\Quiz;

class GiveawayPrizeRepository extends BaseRepository
{
    public static function save(array $input, ?GiveawayPrize $giveawayPrize = null): GiveawayPrize
    {
        $isNew = false;

        if (!$giveawayPrize) {
            $giveawayPrize = new GiveawayPrize();

            $isNew = true;
        }

        static::_fill([
            'name',
            'description',
            'quantity',
            'min_position',
            'max_position',
        ], $input, $giveawayPrize);

        static::_fillBelongs([
            'giveaway',
        ], $input, $giveawayPrize);

        $giveawayPrize->saveOrFail();

        static::_fillMedia([
            'image',
        ], $input, $giveawayPrize);

        $giveawayPrize->refresh();

        return $giveawayPrize;
    }
}
