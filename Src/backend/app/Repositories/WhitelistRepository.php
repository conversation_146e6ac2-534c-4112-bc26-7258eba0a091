<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\Whitelist;

class WhitelistRepository extends BaseRepository
{
    public static function save(array $input, ?Whitelist $whitelist = null): Whitelist
    {
        $isNew = !$whitelist;

        if ($isNew) {
            $whitelist = new Whitelist();
        }

        static::_fill([
            'name',
            'body',
            'field_name',
            'status',
        ], $input, $whitelist);

        $whitelist->saveOrFail();
        $whitelist->refresh();

        return $whitelist;
    }
}
