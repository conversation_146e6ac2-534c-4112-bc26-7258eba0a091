<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\MultimediaConfig;

class MultimediaConfigRepository extends BaseRepository
{
    public static function save(array $input, ?MultimediaConfig $multimediaConfig = null): MultimediaConfig
    {
        $isNew = false;

        if (!$multimediaConfig) {
            $multimediaConfig = new MultimediaConfig();

            $isNew = true;
        }

        static::_fill([
            'logo_link',
            'banner_link',
        ], $input, $multimediaConfig);

        $multimediaConfig->saveOrFail();

        static::_fillMedia([
            'logo',
            'bg_video',
            'bg_image',
            'monitor_bg_video',
            'monitor_bg_image',
            'banner',
        ], $input, $multimediaConfig);

        $multimediaConfig->refresh();

        return $multimediaConfig;
    }
}
