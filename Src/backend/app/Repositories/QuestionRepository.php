<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\LanguageEnum;
use App\Enums\QuestionStatusEnum;
use App\Models\Answer;
use App\Models\Question;
use App\Models\ThemeTag;

class QuestionRepository extends BaseRepository
{
    public static function save(array $input, ?Question $question = null): Question
    {
        $isNew = false;

        if (!$question) {
            $question = new Question();
            $question->status = QuestionStatusEnum::Active;
            $question->lang = LanguageEnum::Es;

            $isNew = true;
        }

        if (array_key_exists('image_url', $input)) {
            if ($input['image_url']) {
                $input['image'] = $input['image_url'];
            }
        }

        if (array_key_exists('audio_url', $input)) {
            if ($input['audio_url']) {
                $input['audio'] = $input['audio_url'];
            }
        }

        static::_fill([
            'body',
            'is_public',
            'type',
            'status',
            'lang'
        ], $input, $question);

        $question->saveOrFail();

        static::_fillManies([
            'answers' => [AnswerRepository::class, Answer::class, 'question'],
        ], $input, $question);

        static::_fillManyBelongs([
            'theme_tags' => [ThemeTagRepository::class, ThemeTag::class],
        ], $input, $question);

        static::_fillMedia([
            'image',
            'audio',
        ], $input, $question);

        $question->refresh();

        return $question;
    }
}
