<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\RegisterConfigField;

class RegisterConfigFieldRepository extends BaseRepository
{
    public static function save(array $input, ?RegisterConfigField $registerConfigField = null): RegisterConfigField
    {
        $isNew = false;

        if (!$registerConfigField) {
            $registerConfigField = new RegisterConfigField();

            $isNew = true;
        }

        if (array_key_exists('label', $input)) {
            $registerConfigField->name = $input['label'];
        }

        static::_fill([
            'label',
            'type',
            'validations',
            'show_in_ranking',
            'label_for_ranking',
        ], $input, $registerConfigField);

        static::_fillBelongs([
            'config',
        ], $input, $registerConfigField);

        $registerConfigField->saveOrFail();
        $registerConfigField->refresh();

        return $registerConfigField;
    }
}
