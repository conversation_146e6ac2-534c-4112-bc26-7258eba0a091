<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\QuestionSetStatusEnum;
use App\Models\Question;
use App\Models\QuestionSet;
use App\Models\ThemeTag;

class QuestionSetRepository extends BaseRepository
{
    public static function save(array $input, ?QuestionSet $questionSet = null): QuestionSet
    {
        $isNew = false;

        if (!$questionSet) {
            $questionSet = new QuestionSet();
            $questionSet->status = QuestionSetStatusEnum::Active;

            $isNew = true;
        }

        static::_fill([
            'name',
            'is_public',
            'status',
        ], $input, $questionSet);

        static::_fillBelongs([
            'owner',
        ], $input, $questionSet);

        $questionSet->saveOrFail();

        static::_fillManyBelongs([
            'theme_tags' => [ThemeTagRepository::class, ThemeTag::class],
            'questions' => [QuestionRepository::class, Question::class],
        ], $input, $questionSet);

        $questionSet->refresh();

        return $questionSet;
    }
}
