<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\StyleConfig;

class StyleConfigRepository extends BaseRepository
{
    public static function save(array $input, ?StyleConfig $styleConfig = null): StyleConfig
    {
        $isNew = !$styleConfig;

        if ($isNew) {
            $styleConfig = new StyleConfig();
        }

        static::_fill([
            'primary',
            'secondary',
            'text_primary',
            'text_secondary',
            'bg_primary',
            'bg_secondary',
            'btn_text_primary',
            'btn_text_secondary',
            'btn_bg_primary',
            'btn_bg_secondary',
            'register_label_text',
            'register_input_text',
            'progress_bar_bg',
            'question_text',
            'question_text_border',
            'answer_btn_text',
            'answer_btn_bg_selected',
            'answer_btn_bg_correct',
            'answer_btn_bg_incorrect',
            'ranking_text',
            'ranking_bg',
            'banner_btn_text',
            'banner_btn_bg',
        ], $input, $styleConfig);

        $styleConfig->saveOrFail();

        static::_fillMedia([
            'css_file',
        ], $input, $styleConfig);

        $styleConfig->refresh();

        return $styleConfig;
    }
}
