<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\QuizGroup;

class QuizGroupRepository extends BaseRepository
{
    public static function save(array $input, ?QuizGroup $quizGroup = null): QuizGroup
    {
        $isNew = !$quizGroup;

        if ($isNew) {
            $quizGroup = new QuizGroup();
        }

        static::_fill([
            'name',
            'max_users',
        ], $input, $quizGroup);

        static::_fillBelongs([
            'quiz',
            'whitelist' => WhitelistRepository::class,
        ], $input, $quizGroup);

        $quizGroup->saveOrFail();
        $quizGroup->refresh();

        return $quizGroup;
    }
}
