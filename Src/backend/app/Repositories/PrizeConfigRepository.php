<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\PrizeConfig;

class PrizeConfigRepository extends BaseRepository
{
    public static function save(array $input, ?PrizeConfig $prizeConfig = null): PrizeConfig
    {
        $isNew = false;

        if (!$prizeConfig) {
            $prizeConfig = new PrizeConfig();

            $isNew = true;
        }

        static::_fill([
            'description',
            'from_position',
            'to_position',
        ], $input, $prizeConfig);

        static::_fillBelongs([
            'config',
        ], $input, $prizeConfig);

        $prizeConfig->saveOrFail();

        static::_fillMedia([
            'image',
        ], $input, $prizeConfig);

        $prizeConfig->refresh();

        return $prizeConfig;
    }
}
