<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\RoleEnum;
use App\Enums\SystemUserStatusEnum;
use App\Enums\SystemUserTypeEnum;
use App\Models\Channel;
use App\Models\SystemUser;

class SystemUserRepository extends BaseRepository
{
    public static function save(array $input, ?SystemUser $systemUser = null): SystemUser
    {
        $isNew = !$systemUser;

        if ($isNew) {
            $systemUser = new SystemUser();
            $systemUser->status = SystemUserStatusEnum::Active;
            $systemUser->type = $input['type'];
        }

        static::_fill([
            'first_name',
            'last_name',
            'email',
            'password',
            'social_id',
            'social_driver',
            'social_avatar',
            'status',
        ], $input, $systemUser);

        static::_fillBelongs([
            'parent',
        ], $input, $systemUser);

        $systemUser->saveOrFail();

        static::_fillManies([
            'channels' => [ChannelRepository::class, Channel::class, 'system_user'],
        ], $input, $systemUser);

        static::_fillMedia([
            'picture',
            'photos' => true,
        ], $input, $systemUser);

        if ($isNew) {
            switch ($systemUser->type) {
                case SystemUserTypeEnum::Administrator: $systemUser->assignRole(RoleEnum::Admin->name); break;
                case SystemUserTypeEnum::Client: $systemUser->assignRole(RoleEnum::Client->name); break;
                case SystemUserTypeEnum::ClientChild: $systemUser->assignRole(RoleEnum::ClientChild->name); break;

                default: break;
            }
        }

        $systemUser->refresh();

        return $systemUser;
    }
}
