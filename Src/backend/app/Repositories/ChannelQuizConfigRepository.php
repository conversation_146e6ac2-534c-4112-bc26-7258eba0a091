<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Models\ChannelQuizConfig;
use App\Models\PrizeConfig;
use App\Models\RegisterConfigField;

class ChannelQuizConfigRepository extends BaseRepository
{
    public static function save(array $input, ?ChannelQuizConfig $channelQuizConfig = null): ChannelQuizConfig
    {
        $isNew = false;

        if (!$channelQuizConfig) {
            $channelQuizConfig = new ChannelQuizConfig();

            $isNew = true;
        }

        static::_fill([
            'max_users_per_quiz',
            'time_per_question',
            'message_primary',
            'message_secondary',
            'allow_lose_points',
            'allow_negative_points',
            'use_random_questions',
            'amount_random_questions',
            'show_ranking_between_questions',
            'time_per_ranking',
            'show_correct_answer',
            'play_audio_on_monitor',
            'ask_for_email_at_the_end',
            'scoring_mode',
            'show_ranking_upon_completion',
            'only_correct_winning_message',
            'only_correct_loser_message',
            'minimum_correct_answers',
        ], $input, $channelQuizConfig);

        static::_fillBelongs([
            'multimedia_config' => MultimediaConfigRepository::class,
            'style_config' => StyleConfigRepository::class,
            'whitelist' => WhitelistRepository::class,
        ], $input, $channelQuizConfig);

        $channelQuizConfig->saveOrFail();

        static::_fillManies([
            'prize_configs' => [PrizeConfigRepository::class, PrizeConfig::class, 'config'],
            'register_config_fields' => [RegisterConfigFieldRepository::class, RegisterConfigField::class, 'config', true],
        ], $input, $channelQuizConfig);

        $channelQuizConfig->refresh();

        return $channelQuizConfig;
    }
}
