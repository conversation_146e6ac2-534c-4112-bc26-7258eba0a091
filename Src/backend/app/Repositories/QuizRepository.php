<?php

namespace App\Repositories;

use App\Core\Bases\BaseRepository;
use App\Enums\QuizStatusEnum;
use App\Models\Question;
use App\Models\Quiz;
use App\Models\QuizGroup;

class QuizRepository extends BaseRepository
{
    public static function save(array $input, ?Quiz $quiz = null): Quiz
    {
        $isNew = false;

        if (!$quiz) {
            $quiz = new Quiz();
            $quiz->status = QuizStatusEnum::Active;

            $isNew = true;
        }

        static::_fill([
            'name',
            'public_name',
            'starts_at',
            'ends_at',
            'podium',
            'type',
            'status',
            'mode',
            'is_public',
        ], $input, $quiz);

        static::_fillBelongs([
            'config' => ChannelQuizConfigRepository::class,
            'question_set',
            'channel',
        ], $input, $quiz);

        $quiz->saveOrFail();

        static::_fillManyBelongs([
            'questions' => [QuestionRepository::class, Question::class],
        ], $input, $quiz);

        static::_fillManies([
            'groups' => [QuizGroupRepository::class, QuizGroup::class, 'quiz', true],
        ], $input, $quiz);

        if ($isNew) {
            $quiz->generatePin();
            $quiz->generateMonitorPin();
        }

        $quiz->refresh();

        return $quiz;
    }
}
