<?php

namespace App\DynamoDbModels\Traits\UserQuizProgress;

use App\DynamoDbModels\QuizRanking;
use App\DynamoDbModels\QuizItineraryStepRanking;
use App\DynamoDbModels\SyncedQuiz;
use App\Enums\QuizTypeEnum;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use App\Websockets\QuizRankingChangedSocket;

trait UserQuizProgressRankingTrait
{
    public function updateRankings(bool $mustRegenerate = false, bool $mustDispatch = true): void
    {
        $quiz = SyncedQuiz::find($this->quiz_id);

        if ($quiz) {
            if (!$mustRegenerate && $quiz->type === QuizTypeEnum::OnDemand->name && $quiz->config['show_ranking_between_questions']) {
                $step = collect($quiz->itinerary->steps)->first(function ($item) {
                    return $item['id'] >= $this->step_id && $item['type'] === ItineraryStepTypeEnum::Ranking->name;
                });

                if ($step) {
                    $this->_updatePartialRanking($quiz, $step);
                }
            }

            $this->_updateFinalRanking($quiz, $mustRegenerate);

            if ($mustDispatch) {
                QuizRankingChangedSocket::dispatch($quiz->id);
            }
        }
    }

    /* -------------------- */

    private function _updateFinalRanking(SyncedQuiz $quiz, bool $mustRegenerate = false): void
    {
        $oldRanking = $quiz->ranking;
        $userQuizProfile = $mustRegenerate ? null : $this->profile;

        $newRanking = $quiz->getFinalRanking($userQuizProfile)->toArray();

        $oldRanking
            ? $oldRanking->update($newRanking)
            : (new QuizRanking())->create($newRanking)
            ;
    }

    private function _updatePartialRanking(SyncedQuiz $quiz, array $step): void
    {
        $oldRanking = QuizItineraryStepRanking::where('quiz_id', $this->quiz_id)->where('step_id', $step['id'])->first();
        $userQuizProfile = $this->profile;

        $newRanking = $quiz->getPartialRanking($step, $userQuizProfile)->toArray();
        $newRanking['step_id'] = $step['id'];

        $oldRanking
            ? $oldRanking->update($newRanking)
            : (new QuizItineraryStepRanking())->create($newRanking);
            ;
    }
}
