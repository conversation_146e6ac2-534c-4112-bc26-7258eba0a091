<?php

namespace App\DynamoDbModels\Traits\UserQuizProgress;

trait UserQuizProgressPointsTrait
{
    public function calcPoints(): void
    {
        if($this->points_earned > -1) {
            $this->traditionalCalcPoints();
        } else {
            $this->points_earned = 0;

            if ($this->selected_answer_is_correct) {
                $this->points_earned = 1;
            }

            $this->counterCorrectCalcPoints();
        }
    }

    public function traditionalCalcPoints(): void
    {
        $pointsBaseEarned = 10000;
        $pointsEarned = ($pointsBaseEarned + $this->points_earned) * $this->points_multiplier;

        if (!$this->selected_answer_is_correct) {
            $pointsEarned = $this->allow_lose_points ? $pointsEarned * -1 : 0;
        }

        $pointsNow = $pointsEarned + $this->points_before;

        if ($pointsNow < 0 && !$this->allow_negative_points) {
            $pointsNow = 0;
        }

        $this->points_multiplier = $this->points_multiplier;
        $this->points_base_earned = $pointsBaseEarned;
        $this->points_earned = $pointsEarned;
        $this->points_before = $this->points_before;
        $this->points_now = $pointsNow;
    }

    public function counterCorrectCalcPoints(): void
    {
        $pointsBaseEarned = 0;
        $pointsEarned = ($pointsBaseEarned + $this->points_earned);

        $pointsNow = $pointsEarned + $this->points_before;

        $this->points_multiplier = $this->points_multiplier;
        $this->points_base_earned = $pointsBaseEarned;
        $this->points_earned = $pointsEarned;
        $this->points_before = $this->points_before;
        $this->points_now = $pointsNow;
    }
}
