<?php

namespace App\DynamoDbModels\Traits\SyncedQuiz;

use App\DynamoDbModels\SyncedQuiz;
use App\Enums\QuestionTypeEnum;
use App\Enums\QuizEventTypeEnum;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

trait SyncedQuizReportTrait
{
    public function getReport(bool $withAnswerUsersReport = true)
    {
        $report = [];

        $questions = collect($this->question_set['questions']);

        if (!$userProfiles = Cache::get("UserQuizProfiles:$this->id", null)) return;

        $userProfiles = collect($userProfiles['data'] ?? []);

        $report['is_multiple'] = false;
        $report['use_groups'] = !$this->groups->isEmpty();
        $report['quiz_type_enum'] = $this->type_enum;
        $report['quiz_last_event'] = $this->last_event;
        $report['quiz_register_config_fields'] = $this->config['register_config_fields'];
        $report['questions_count']['total'] = $questions->count();
        $report['questions_count'][QuestionTypeEnum::Double->name] = $questions->where('type', QuestionTypeEnum::Double)->count();
        $report['questions_count'][QuestionTypeEnum::Bomb->name] = $questions->where('type', QuestionTypeEnum::Bomb)->count();
        $report['questions_count'][QuestionTypeEnum::Survey->name] = $questions->where('type', QuestionTypeEnum::Survey)->count();

        foreach ($questions as $question) {
            $reportQuestion = collect($question)->except(['image', 'audio', 'media', 'pivot'])->toArray();
            $reportQuestion['users_count'] = 0;

            $reportAnswers = collect();

            foreach ($question['answers'] as $answer) {
                $reportAnswer = $answer;

                if ($withAnswerUsersReport) {
                    $reportAnswer['users'] = $this->getAnswerUsersReport($answer['id'], $userProfiles);
                }

                $reportAnswer['users_count'] = collect($reportAnswer['users'])->count();
                $reportQuestion['users_count'] += $reportAnswer['users_count'];

                $reportAnswers->push($reportAnswer);
            }

            $reportQuestion['answers'] = $reportAnswers->sortByDesc('users_count')->values()->toArray();
            $reportQuestion = $this->getAnswersPercentage($reportQuestion);

            $report['questions'][] = $reportQuestion;
        }

        $report['ranking'] = $this->getFinalRanking();

        $report['updated_at'] = Carbon::now();
        $report['is_finished'] = $this->state === QuizEventTypeEnum::Finished->name;

        return $report;
    }

    public function getAnswerUsersReport($answerId, $userProfiles): Array
    {
        $report = collect([]);

        foreach ($userProfiles as $profile) {
            $allProgress = $profile->progress;

            foreach ($allProgress as $progress) {
                if ($progress['selected_answer_id'] === $answerId) {
                    $reportItem = [];

                    $profile = collect($userProfiles)->where('quiz_id', $progress->quiz_id)->where('user_id', $progress->user_id)->first();

                    foreach ($this->config['register_config_fields'] as $field) {
                        $reportItem['custom_fields'][] = [
                            'label' => $field['label'],
                            'value' => collect($profile->register_field_values)->firstWhere('field.id', $field['id'])['value'] ?? null
                        ];
                    }

                    $reportItem['points'] = $progress->points_earned;

                    $report->push($reportItem);
                }
            }
        }

        $report = $report
            ->sortByDesc('points')->values()
            ->toArray();

        return $report;
    }

    public function getAnswersPercentage($question) {
        $question['users_count_correct_percentage'] = 0;
        $question['users_count_incorrect_percentage'] = 0;

        $answers = $question['answers'];

        foreach ($answers as $key => $answer) {
            $answers[$key]['users_count_percentage'] = $question['users_count'] > 0
                ? ($answer['users_count'] / $question['users_count']) * 100
                : 0;

            if ($answers[$key]['is_correct']) {
                $question['users_count_correct_percentage'] += $answers[$key]['users_count_percentage'];
            } else {
                $question['users_count_incorrect_percentage'] += $answers[$key]['users_count_percentage'];
            }
        }

        $question['answers'] = $answers;

        return $question;
    }

    /* -------------------- */

    public static function getReports($quizIds): array
    {
        $quizzes = SyncedQuiz::findMany($quizIds);

        $report = [];
        $report['is_multiple'] = true;

        $items = [];

        foreach ($quizzes as $quiz) {
            foreach ($quiz->user_profiles as $profile) {
                $items[] = [
                    'name' => $profile->name,
                    'email' => $profile->email,
                    'points' => $profile->last_progress->points_now ?? 0,
                ];
            }
        }

        $unifiedItems = collect($items)->filter(function ($item) {
            return !empty($item['email']);
        })->groupBy('email')->map(function ($groupedItems) {
            return $groupedItems->reduce(function ($carry, $item) {
                $carry['points'] += $item['points'];
                return $carry;
            }, [
                'name' => $groupedItems->last()['name'],
                'email' => $groupedItems->first()['email'],
                'points' => 0
            ]);
        })->values()->all();

        $nonUnifiedItems = collect($items)->filter(function ($item) {
            return empty($item['email']);
        })->values()->all();

        $report['ranking']['items'] = collect($unifiedItems)
            ->merge($nonUnifiedItems)
            ->sortByDesc('points')
            ->values()
            ->toArray();

        return $report;
    }
}
