<?php

namespace App\DynamoDbModels\Traits\SyncedQuiz;

trait SyncedQuizQuestionTrait
{
    public function getRandomQuestionIds(): array
    {
        $questionIds = collect($this->question_set['questions'])
            ->pluck('id')
            ->shuffle()
            ;

        if ($this->config['amount_random_questions']) {
            $questionIds = $questionIds->take($this->config['amount_random_questions']);
        } else {
            $questionIds = $questionIds->split(2)[0];
        }

        return $questionIds->toArray();
    }
}
