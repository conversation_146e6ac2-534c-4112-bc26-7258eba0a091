<?php

namespace App\DynamoDbModels\Traits\SyncedQuiz;

use App\Enums\ErrorEnum;

trait SyncedQuizGroupTrait
{
    public function resolveGroup($userQuizProfile)
    {
        $groups = collect($this->groups);

        if (!$groups->count()) return;

        $userInWhitelist = false;

        foreach ($this->groups as $group) {
            $whitelist = $group['whitelist'];

            if ($whitelist) {
                $value = collect($userQuizProfile['register_field_values'])->firstWhere('field.type', $whitelist['field_name'])['value'];

                if (in_array($value, $whitelist['body'])) {
                    $userInWhitelist = true;

                    if (!$group['max_users'] || $group->profiles->count() < $group['max_users']) {
                        $userQuizProfile['group_id'] = $group['id'];

                        return $userQuizProfile;
                    } else {
                        ErrorEnum::QUIZ_GROUP_MAX_USERS_LIMIT_REACHED_ERROR->throw();
                    }
                }
            }
        }

        if (!$userInWhitelist) {
            ErrorEnum::QUIZ_WHITELIST_ERROR->throw();
        }
    }
}
