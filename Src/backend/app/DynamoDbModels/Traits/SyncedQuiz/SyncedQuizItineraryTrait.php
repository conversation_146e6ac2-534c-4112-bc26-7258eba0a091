<?php

namespace App\DynamoDbModels\Traits\SyncedQuiz;

use App\DynamoDbModels\QuizItinerary;
use App\Enums\QuizTypeEnum;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;

trait SyncedQuizItineraryTrait
{
    public function getItinerary($withAuth = false): QuizItinerary
    {
        $itinerary = QuizItinerary::where('quiz_id', $this->id)->first();

        if ($withAuth) {
            $authUserQuestionIds = $this->auth_user_profile['question_ids'] ?? collect($this->question_set['questions'])->pluck('id')->toArray();
            $currentQuestion = null;
            $itineraryStepRankings = $this->itinerary_step_rankings;

            $itinerary->steps = collect($itinerary->steps)->map(function ($step) use ($itineraryStepRankings, &$authUserQuestionIds, &$currentQuestion) {
                $step['progress'] = collect($this->auth_user_progress)->where('step_id', $step['id'])->first();
                $step['ranking'] = collect($itineraryStepRankings)->firstWhere('step_id', $step['id']);

                $questions = $this->question_set['questions'];

                if ($step['type'] === 'Question') {
                    $questionId = array_shift($authUserQuestionIds);

                    $currentQuestion = collect($questions)->firstWhere('id', $questionId);
                    $currentQuestion['answers'] = collect($currentQuestion['answers'])->shuffle();

                    $step['question'] = $currentQuestion;
                }

                if ($step['type'] === 'QuestionResults' || $step['type'] === 'Ranking') {
                    $step['question'] = $currentQuestion;
                }

                return $step;
            })->all();

            if ($currentStepId = $this->auth_user_profile['current_step_id'] ?? null) {
                do {
                    $itinerary['currentStep'] = collect($itinerary->steps)->where('id', $currentStepId)->first();
                    $currentStepId++;
                } while ($itinerary['currentStep']['type'] === ItineraryStepTypeEnum::Question->name);
            }
        }

        return $itinerary;
    }
}
