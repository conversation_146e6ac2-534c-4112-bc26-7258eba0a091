<?php

namespace App\DynamoDbModels\Traits\SyncedQuiz;

use App\DynamoDbModels\UserQuizProfile;
use App\DynamoDbModels\UserQuizProgress;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use Illuminate\Support\Collection;

trait SyncedQuizRankingTrait
{
    public function getPartialRanking(?array $step = null, ?UserQuizProfile $userQuizProfile = null): ?Collection
    {
        if ($step) {
            if (!in_array($step['type'], [
                ItineraryStepTypeEnum::QuestionResults->name,
                ItineraryStepTypeEnum::Ranking->name,
                ItineraryStepTypeEnum::FinalRanking->name,
                ItineraryStepTypeEnum::Results->name,
                ItineraryStepTypeEnum::FinalResults->name,
            ])) {
                return null;
            }
        }

        if ($this->groups->isEmpty()) {
            $rankingItems = $step
                ? $this->_getRankingItemsByStep($step)
                : $this->_getRankingItems($userQuizProfile)
                ;
        } else {
            $rankingItems = $this->_getRankingItemsByGroup($step);
        }

        // for ($i = 0; $i < 20; $i++) {
        //     $rankingItems->add(collect([
        //         'user_profile_id' => -1,
        //         'name' => 'Test',
        //         'points' => rand(0, 100000),
        //         'its-me' => $i === 0 ? true : false,
        //     ]));
        // }

        if ($rankingItems) {
            $rankingItems = $rankingItems
                ->sortByDesc('points')->values()
                ->map(function ($item, $index) {
                    return collect($item)->put('position', $index + 1);
                })
                ;
        }

        return collect([
            'quiz_id' => $this->id,
            'items' => $rankingItems,
        ]);
    }

    public function getFinalRanking(?UserQuizProfile $userQuizProfile = null): ?Collection
    {
        return $this->getPartialRanking(null, $userQuizProfile);
    }

    /* -------------------- */

    private function _getRankingItems(?UserQuizProfile $userQuizProfile = null): ?Collection
    {
        if ($userQuizProfile) {
            $rankingItems = $this->ranking->items ?? [];
            $userQuizProgress = collect($userQuizProfile->progress)->sortBy('step_id')->values();

            $itemIndex = collect($rankingItems)->search(function ($item) use ($userQuizProfile) {
                return $item['user_profile_id'] === $userQuizProfile['id'];
            });

            if ($itemIndex !== false) {
                $rankingItems[$itemIndex]['points'] = collect($userQuizProgress)->last()['points_now'] ?? 0;
            } else {
                $rankingItems[] = collect([
                    'quiz_id' => $this->id,
                    'user_profile_id' => $userQuizProfile['id'],
                    'name' => $userQuizProfile['name'],
                    'email' => $userQuizProfile['email'],
                    'extra_register_data' =>$userQuizProfile['extra_register_data_for_ranking'],
                    'custom_fields' => $userQuizProfile->custom_fields,
                    'points' => collect($userQuizProgress)->last()['points_now'] ?? 0,
                    'podium' => $this->podium,
                ]);
            }

            return collect($rankingItems);
        }

        $userQuizProfiles = $this->user_profiles;

        return $userQuizProfiles
            ->map(function ($userQuizProfile) {
                $userQuizProgress = collect($userQuizProfile->progress)->sortBy('step_id')->values();

                return collect([
                    'quiz_id' => $this->id,
                    'user_profile_id' => $userQuizProfile['id'],
                    'name' => $userQuizProfile['name'],
                    'email' => $userQuizProfile['email'],
                    'extra_register_data' => $userQuizProfile['extra_register_data_for_ranking'],
                    'custom_fields' => $userQuizProfile->custom_fields,
                    'points' => collect($userQuizProgress)->last()['points_now'] ?? 0,
                    'podium' => $this->podium,
                ]);
            });
    }

    private function _getRankingItemsByStep(array $step): ?Collection
    {
        $userQuizProfiles = $this->user_profiles;

        return $userQuizProfiles
            ->map(function ($userQuizProfile) use ($step) {
                $userQuizProgress = collect($userQuizProfile->progress)->sortBy('step_id')->values();

                return collect([
                    'quiz_id' => $this->id,
                    'user_profile_id' => $userQuizProfile['id'],
                    'name' => $userQuizProfile['name'],
                    'email' => $userQuizProfile['email'],
                    'extra_register_data' => $userQuizProfile['extra_register_data_for_ranking'],
                    'custom_fields' => $userQuizProfile->custom_fields,
                    'points' => collect($userQuizProgress)->reverse()->firstWhere('step_id', '<=', $step['id'])['points_now'] ?? 0,
                    'podium' => $this->podium,
                ]);
            });
    }

    private function _getRankingItemsByGroup(?array $step = null): ?Collection
    {
        return collect($this->groups)
            ->map(function ($group) use ($step) {
                $profiles = collect($group->profiles);

                return collect([
                    'quiz_id' => $this->id,
                    'quiz_group_id' => $group['id'],
                    'users_in_group_count' => $profiles->count(),
                    'name' => $group['name'],
                    'points' => round($profiles
                        ->map(function ($profile) use ($step) {
                            $userQuizProgress = collect($profile->progress)->sortBy('step_id')->values();

                            return collect([
                                'points' => $step
                                    ? collect($userQuizProgress)->reverse()->firstWhere('step_id', '<=', $step['id'])['points_now'] ?? 0
                                    : collect($userQuizProgress)->last()['points_now'] ?? 0,
                            ]);
                        })->avg('points') ?? 0),
                    'podium' => $this->podium,
                ]);
            });
    }
}
