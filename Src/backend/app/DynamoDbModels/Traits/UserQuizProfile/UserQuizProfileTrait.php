<?php

namespace App\DynamoDbModels\Traits\UserQuizProfile;

use App\DynamoDbModels\SyncedQuizUsersCount;
use App\Websockets\QuizUsersCountChangedSocket;

trait UserQuizProfileTrait
{
    public function updateQuizUsersCount($deleted = false, $hasWebsocketConnection): void
    {
        $oldQuizUsersCount = SyncedQuizUsersCount::find($this->quiz_id);
        $usersCount = $oldQuizUsersCount->users_count ?? 0;
        $websocketConnections = $oldQuizUsersCount->websocket_connections ?? 0;

        $newUsersCount['id'] = $oldQuizUsersCount->id ?? $this->quiz_id;
        $newUsersCount['users_count'] = $deleted ? --$usersCount : ++$usersCount;

        if ($hasWebsocketConnection) {
            $newUsersCount['websocket_connections'] = $deleted ? --$websocketConnections : ++$websocketConnections;
        }

        $oldQuizUsersCount
            ? $oldQuizUsersCount->update($newUsersCount)
            : (new SyncedQuizUsersCount())->create($newUsersCount)
            ;

        QuizUsersCountChangedSocket::dispatch($this->quiz_id);
    }
}
