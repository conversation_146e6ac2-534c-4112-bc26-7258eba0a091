<?php

namespace App\DynamoDbModels;

use App\Core\Bases\BaseDynamoDbModel;
use App\DynamoDbModels\Traits\UserQuizProfile\UserQuizProfileTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class UserQuizProfile extends BaseDynamoDbModel
{
    use UserQuizProfileTrait;

    protected $dynamoDbIndexKeys = [
        'quiz_user_index' => [
            'hash' => 'quiz_id',
            'range' => 'user_id',
        ],
        'quiz_group_index' => [
            'hash' => 'quiz_id',
            'range' => 'group_id',
        ],
    ];

    protected $appends = [
        'group',
        'extra_register_data_for_ranking',
    ];

    protected static function booted(): void
    {
        static::saved(function (UserQuizProfile $userQuizProfile) {
            if (!$userQuizProfile->current_step_id) {
                $userQuizProfile->updateQuizUsersCount(false, $userQuizProfile->has_websocket_connection);
            }
        });

        static::deleted(function (UserQuizProfile $userQuizProfile) {
           if (!empty($userQuizProfile->progress)) {
                $userQuizProfile->progress->each(fn ($item) => $item->delete());
            }

            $cachedUserQuizProfile = Cache::get("UserQuizProfiles:$userQuizProfile->quiz_id");

            if ($cachedUserQuizProfile) {
                Cache::put("UserQuizProfiles:$userQuizProfile->quiz_id", [
                    'data' => collect(Cache::get("UserQuizProfiles:$userQuizProfile->quiz_id")['data'])->filter(fn ($item) => $item->id !== $userQuizProfile->id)->values(),
                    'updated_at' => Carbon::now(),
                ]);
            }

            $userQuizProfile->updateQuizUsersCount(true, $userQuizProfile->has_websocket_connection);

            $syncedQuiz = SyncedQuiz::find($userQuizProfile->quiz_id);
            if ($syncedQuiz) {
                $report = $syncedQuiz->getReport();

                if ($report) {
                    Cache::put("QuizReport:$syncedQuiz->id", $report);
                };

            }
        });
    }

    /**
     * Accesors & Mutators.
     */
    protected function progress(): Attribute
    {
        return Attribute::make(
            get: fn () => UserQuizProgress::where('quiz_id', $this->quiz_id)->where('user_id', $this->user_id)->get(),
        );
    }

    protected function lastProgress(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->progress ? $this->progress->sortBy('step_id')->last() : null,
        );
    }

    protected function quiz(): Attribute
    {
        return Attribute::make(
            get: fn () => SyncedQuiz::find($this->quiz_id),
        );
    }

    protected function group(): Attribute
    {
        return Attribute::make(
            get: fn () => SyncedQuizGroup::where('quiz_id', $this->quiz_id)->where('id', $this->group_id)->first(),
        );
    }

    protected function extraRegisterDataForRanking(): Attribute
    {
        return Attribute::make(
            get: fn () => collect($this->register_field_values)->where('field.show_in_ranking', 1)->map(function ($item) {
                return [
                    'value' => $item['value'],
                    'label' => $item['field']['label_for_ranking']
                ];
            })->values() ?? null,
        );
    }

    protected function customFields(): Attribute
    {
        return Attribute::make(
            get: fn () => collect($this->register_field_values)->map(function ($item) {
                return [
                    'value' => $item['value'],
                    'label' => $item['field']['label']
                ];
            })->values() ?? null,
        );
    }
}
