<?php

namespace App\DynamoDbModels;

use App\Core\Bases\BaseDynamoDbModel;
use App\DynamoDbModels\Traits\UserQuizProgress\UserQuizProgressTrait;
use App\Enums\QuizTypeEnum;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserQuizProgress extends BaseDynamoDbModel
{
    use UserQuizProgressTrait;

    protected $dynamoDbIndexKeys = [
        'quiz_user_index' => [
            'hash' => 'quiz_id',
            'range' => 'user_id',
        ],
    ];

    protected static function booted(): void
    {
        static::saving(function (UserQuizProgress $userQuizProgress) {
            if (!$userQuizProgress->sync_id) {
                $userQuizProgress->calcPoints();
            }
        });

        static::saved(function (UserQuizProgress $userQuizProgress) {
            if ($userQuizProgress->quiz_type === QuizTypeEnum::OnDemand->name) {
                !!$userQuizProgress->sync_id
                    ? $userQuizProgress->updateRankings(true, false)
                    : $userQuizProgress->updateRankings();
            }
        });

        static::deleted(function (UserQuizProgress $userQuizProgress) {
            $userQuizProgress->updateRankings(true);
        });
    }

    /**
     * Accesors & Mutators.
     */
    protected function profile(): Attribute
    {
        return Attribute::make(
            get: fn () => UserQuizProfile::find("quizId:{$this->quiz_id}_" . "userId:{$this->user_id}"),
        );
    }
}
