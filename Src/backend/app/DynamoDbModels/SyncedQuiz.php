<?php

namespace App\DynamoDbModels;

use App\Core\Bases\BaseDynamoDbModel;
use App\DynamoDbModels\Traits\SyncedQuiz\SyncedQuizTrait;
use App\Facades\Auth;
use Illuminate\Database\Eloquent\Casts\Attribute;

class SyncedQuiz extends BaseDynamoDbModel
{
    use SyncedQuizTrait;

    protected $hidden = [
        'monitor_pin',
        'pin'
    ];

    protected static function booted(): void
    {
        static::deleted(function (SyncedQuiz $syncedQuiz) {
            if ($syncedQuiz->raw_itinerary) {
                $syncedQuiz->raw_itinerary->delete();
            }

            if ($syncedQuiz->ranking) {
                $syncedQuiz->ranking->delete();
            }

           if (!empty($syncedQuiz->itinerary_step_rankings)) {
                $syncedQuiz->itinerary_step_rankings->each(fn ($ranking) => $ranking->delete());
            }

            if (!empty($syncedQuiz->user_profiles)) {
                $syncedQuiz->user_profiles->each(fn ($profile) => $profile->delete());
            }

            if (!empty($syncedQuiz->user_progress)) {
                $syncedQuiz->user_progress->each(fn ($progress) => $progress->delete());
            }

            if (!empty($syncedQuiz->groups)) {
                $syncedQuiz->groups->each(fn ($group) => $group->delete());
            }
        });
    }

    /**
     * Accesors & Mutators.
     */
    protected function rawItinerary(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getItinerary(),
        );
    }

    protected function itinerary(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->getItinerary(!!Auth::user()),
        );
    }

    protected function ranking(): Attribute
    {
        return Attribute::make(
            get: fn () => QuizRanking::where('quiz_id', $this->id)->first(),
        );
    }

    protected function itineraryStepRankings(): Attribute
    {
        return Attribute::make(
            get: fn () => QuizItineraryStepRanking::where('quiz_id', $this->id)->get(),
        );
    }

    protected function userProfiles(): Attribute
    {
        return Attribute::make(
            get: fn () => UserQuizProfile::where('quiz_id', $this->id)->get(),
        );
    }

    protected function authUserProfile(): Attribute
    {
        return Attribute::make(
            get: fn () => Auth::user()
                            ? UserQuizProfile::find("quizId:{$this->id}_" . "userId:" . Auth::user()->id)
                            : null,
        );
    }

    protected function userProgress(): Attribute
    {
        return Attribute::make(
            get: fn () => UserQuizProgress::where('quiz_id', $this->id)->withIndex('quiz_user_index')->get(),
        );
    }

    protected function authUserProgress(): Attribute
    {
        return Attribute::make(
            get: fn () => Auth::user()
                            ? collect(UserQuizProgress::where('quiz_id', $this->id)
                                ->where('user_id', Auth::user()->id)
                                ->get())
                                ->sortBy('step_id')
                                ->values()
                            : null,
        );
    }

    protected function groups(): Attribute
    {
        return Attribute::make(
            get: fn () => SyncedQuizGroup::where('quiz_id', $this->id)->get(),
        );
    }

    protected function hasRoom(): Attribute
    {
        return Attribute::make(
            get: fn () => !!$this->config['max_users_per_quiz']
                ? $this->users_count < $this->config['max_users_per_quiz']
                : true
        );
    }

    protected function usersCount(): Attribute
    {
        return Attribute::make(
            get: fn () => SyncedQuizUsersCount::find($this->id)->users_count ?? 0,
        );
    }
}
