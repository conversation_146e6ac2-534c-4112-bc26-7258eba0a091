<?php

namespace App\DynamoDbModels;

use App\Core\Bases\BaseDynamoDbModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class SyncedQuizGroup extends BaseDynamoDbModel
{
    protected $dynamoDbIndexKeys = [
        'quiz_index' => [
            'hash' => 'quiz_id',
        ],
    ];

    /**
     * Accesors & Mutators.
     */
    protected function profiles(): Attribute
    {
        return Attribute::make(
            get: fn () => UserQuizProfile::where('quiz_id', $this->quiz_id)
                ->where('group_id', $this->id)
                ->withIndex('quiz_group_index')
                ->get(),
        );
    }
}
