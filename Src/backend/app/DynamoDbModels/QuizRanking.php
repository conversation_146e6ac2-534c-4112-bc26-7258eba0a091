<?php

namespace App\DynamoDbModels;

use App\Core\Bases\BaseDynamoDbModel;
use App\Storages\RankingStorage;
use Illuminate\Database\Eloquent\Casts\Attribute;

class QuizRanking extends BaseDynamoDbModel
{
    protected $dynamoDbIndexKeys = [
        'quiz_index' => [
            'hash' => 'quiz_id',
        ],
    ];

    protected $appends = [
        'items',
    ];

    /**
     * Accesors & Mutators.
     */
    protected function items(): Attribute
    {
        return Attribute::make(
            get: fn () => ($this->storage_path ? json_decode(RankingStorage::instance()->get($this->storage_path)) : $this->attributes['items']) ?? [],
        );
    }
}
