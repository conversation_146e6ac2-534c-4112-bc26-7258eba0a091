<?php

namespace App\Sync;

use App\Core\Sync\SyncModel;
use App\DynamoDbModels\SyncedQuizGroup;
use App\Models\Quiz;
use App\Models\QuizGroup;

class QuizGroupSync extends SyncModel
{
    protected $_models = [
        QuizGroup::class,
        /* -------------------- */
        Quiz::class,
    ];

    public function run(mixed $params)
    {
        $model = $params['model'] ?? null;

        $quizGroupIds = [];
        $deleted = false;

        switch (true) {
            case $model instanceof QuizGroup: {
                $quizGroupIds[] = $model->id;
                $deleted = !!$model->deleted_at;

                break;
            }

            case $model instanceof Quiz: {
                foreach ($model->groups as $modelGroup) {
                    $quizGroupIds[] = $modelGroup->id;
                }

                break;
            }
        }

        foreach ($quizGroupIds as $quizGroupId) {
            $quizGroup = QuizGroup::select()
                ->with('whitelist')
                ->find($quizGroupId)
                ;

            if ($quizGroup) {
                $quizGroupData = $quizGroup->toArray();

                $syncedQuizGroup = SyncedQuizGroup::where('sync_id', $quizGroupId)->first();
                $syncedQuizGroup
                    ? $syncedQuizGroup->update($quizGroupData)
                    : SyncedQuizGroup::create($quizGroupData);
            } else if ($deleted) {
                $syncedQuizGroup = SyncedQuizGroup::find($quizGroupId);

                if ($syncedQuizGroup) {
                    SyncedQuizGroup::find($quizGroupId)->delete();
                }
            }
        }
    }
}

