<?php

namespace App\Sync;

use App\Core\Sync\SyncModel;
use App\DynamoDbModels\QuizRanking;
use App\Features\Itinerary\Enums\ItineraryStepTypeEnum;
use App\Models\Quiz;
use App\Models\UserQuizProgress;
use App\Websockets\QuizRankingChangedSocket;
use Illuminate\Database\Eloquent\Model;

class UserQuizProgressSync extends SyncModel
{
    protected $_models = [
        // UserQuizProgress::class,
    ];

    public function run(mixed $params)
    {
        $model = $params['model'] ?? null;
        $mustDispatchSocket = array_key_exists('avoidSocketDispatch', $params) ? !$params['avoidSocketDispatch'] : true;

        $userQuizProgress = UserQuizProgress::select()
            ->where('user_quiz_id', $model->user_quiz_id)
            ->with('selected_answer')
            ->find($model->id)
            ;

        if ($userQuizProgress) {
            $userQuizProgressData = $userQuizProgress->toArray();
            $userQuizProgressData['id'] = null;
            $userQuizProgressData['sync_id'] = $userQuizProgress->id;
            $userQuizProgressData['quiz_id'] = $userQuizProgress->user_quiz->quiz->id;
            $userQuizProgressData['user_id'] = $userQuizProgress->user_quiz->user->id;

            \App\DynamoDbModels\UserQuizProgress::create($userQuizProgressData);
        } else {
            if ($dynamoUserQuizProgress = \App\DynamoDbModels\UserQuizProgress::where('sync_id', $model->id)->first()) {
                $dynamoUserQuizProgress->delete();
            }
        }
    }
}

