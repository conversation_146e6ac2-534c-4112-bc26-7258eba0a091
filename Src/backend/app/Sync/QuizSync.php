<?php

namespace App\Sync;

use App\Core\Sync\SyncModel;
use App\DynamoDbModels\QuizItinerary;
use App\DynamoDbModels\SyncedQuiz;
use App\Models\QuestionSet;
use App\Models\Quiz;
use App\Models\QuizEvent;
use App\Models\UserQuizPivot;
use App\Websockets\QuizChangedSocket;

class QuizSync extends SyncModel
{
    protected $_models = [
        Quiz::class,
        /* -------------------- */
        QuizEvent::class,
        QuestionSet::class,
        UserQuizPivot::class,
    ];

    public function run(mixed $params)
    {
        $model = $params['model'] ?? null;
        $event = $params['event'] ?? null;
        $mustDispatchSocket = array_key_exists('avoidSocketDispatch', $params) ? !$params['avoidSocketDispatch'] : true;

        $quizIds = [];
        $deleted = false;

        switch (true) {
            case $model instanceof Quiz: {
                $quizIds[] = $model->id;
                $deleted = $event === 'deleted';

                break;
            }

            case $model instanceof QuizEvent: {
                $quizIds[] = $model->quiz_id;

                break;
            }

            case $model instanceof QuestionSet: {
                foreach ($model->quizzes as $modelQuiz) {
                    $quizIds[] = $modelQuiz->id;
                }

                break;
            }

            case $model instanceof UserQuizPivot: {
                $quizIds[] = $model->quiz_id;
            }
        }

        foreach ($quizIds as $quizId) {
            $quiz = Quiz::select()
                ->withConfigFull()
                ->withCount('users')
                ->with([
                    'question_set' => function ($query) {
                        $query->with(['questions' => function ($query) {
                            $query->withSortedAnswers();
                        }]);
                    },
                    'groups.whitelist',
                ])
                ->find($quizId)
                ;

            if ($quiz) {
                $quiz->append(['can_access', 'has_room']);

                $quizData = json_decode(json_encode($quiz->toArray()), true);

                $quizItineraryData = json_decode(json_encode($quiz->itinerary), true);
                $quizItineraryData['quiz_id'] = $quiz->id;

                $syncedQuiz = SyncedQuiz::find($quizId);

                if ($syncedQuiz) {
                    $syncedQuiz->update($quizData);

                    $itinerary = $syncedQuiz->raw_itinerary;
                    $itinerary
                        ? $itinerary->update($quizItineraryData)
                        : QuizItinerary::create($quizItineraryData);
                } else {
                    SyncedQuiz::create($quizData);
                    QuizItinerary::create($quizItineraryData);
                }

                if ($mustDispatchSocket) {
                    QuizChangedSocket::dispatch($quiz->id);
                }
            } else if ($deleted) {
                $syncedQuiz = SyncedQuiz::find($quizId);

                if ($syncedQuiz) {
                    SyncedQuiz::find($quizId)->delete();
                }
            }
        }
    }
}

