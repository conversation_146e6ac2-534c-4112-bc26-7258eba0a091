<?php

namespace App\Sync;

use App\Core\Sync\SyncModel;
use App\Models\UserQuizProfile;
use App\Models\UserQuizProgress;
use App\Websockets\QuizUsersCountChangedSocket;
use Illuminate\Database\Eloquent\Model;

class UserQuizProfileSync extends SyncModel
{
    protected $_models = [
        // UserQuizProfile::class,
        // /* -------------------- */
        // UserQuizProgress::class,
    ];

    public function run(mixed $params)
    {
        $model = $params['model'] ?? null;
        $mustDispatchSocket = array_key_exists('avoidSocketDispatch', $params) ? !$params['avoidSocketDispatch'] : true;

        $userQuizProfile = null;

        switch (true) {
            case $model instanceof UserQuizProfile: {
                $userQuizProfile = UserQuizProfile::select()
                    ->with('register_field_values.field')
                    ->find($model->id)
                    ;

                break;
            }

            case $model instanceof UserQuizProgress: {
                $userQuizProfile = UserQuizProfile::select()
                    ->where('user_quiz_id', $model->user_quiz_id)
                    ->with('register_field_values.field')
                    ->first()
                    ;

                break;
            }
        }

        if ($userQuizProfile) {
            $userQuizProfile->append(['group', 'name', 'email', 'dni', 'extra_register_data_for_ranking']);

            $userQuizProfileData = $userQuizProfile->toArray();
            $userQuizProfileData['id'] = null;
            $userQuizProfileData['sync_id'] = $userQuizProfile->id;
            $userQuizProfileData['quiz_id'] = $userQuizProfile->user_quiz->quiz->id;
            $userQuizProfileData['user_id'] = $userQuizProfile->user_quiz->user->id;

            \App\DynamoDbModels\UserQuizProfile::create($userQuizProfileData);

            if ($mustDispatchSocket) {
                QuizUsersCountChangedSocket::dispatch($userQuizProfile->user_quiz->quiz->id);
            }
        } else {
            if ($dynamoUserQuizProfile = \App\DynamoDbModels\UserQuizProfile::where('sync_id', $model->id)->first()) {
                $dynamoUserQuizProfile->delete();
            }
        }
    }
}

