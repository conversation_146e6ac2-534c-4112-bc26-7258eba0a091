<?php

namespace App\Data;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class UserQuizProfileData extends Data
{
    public function __construct(
        public string $id,
        public int $user_id,
        public int $quiz_id,
        public string $quiz_name,
        public int $channel_id,
        public string $channel_name,
        public int|Optional $group_id,
        public array $register_field_values,
        public ?array $question_ids,
        public ?int $current_step_id,
        public ?string $end_email,
        public string $name,
        public ?string $email,
        public ?int $dni,
        public bool $has_websocket_connection,
    ) { }
}
