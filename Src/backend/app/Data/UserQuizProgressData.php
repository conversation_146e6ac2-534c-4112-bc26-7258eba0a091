<?php

namespace App\Data;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class UserQuizProgressData extends Data
{
    public function __construct(
        public string $id,
        public int $quiz_id,
        public int $user_id,
        public int $step_id,
        public string $quiz_type,
        public int $question_id,
        public int $selected_answer_id,
        // public array $selected_answer,
        public bool $selected_answer_is_correct,
        public bool $allow_negative_points,
        public bool $allow_lose_points,
        public int $points_multiplier,
        public ?int $points_base_earned,
        public int $points_earned,
        public int $points_before,
        public ?int $points_now,
        public bool $has_websocket_connection,
    ) { }
}
